import React, { useState, useEffect } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { Slider } from 'primereact/slider';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { ProgressBar } from 'primereact/progressbar';

// Icons
import { FiX, FiRotateCw, FiRotateCcw } from 'react-icons/fi';
import {
    MdOutlineBrightness6,
    MdOutlineContrast,
    MdOutlineBlurOn,
    MdOutlineOpacity,
    MdOutlineCrop,
    MdOutlineAutoFixHigh,
    MdOutlineColorLens,
    MdOutlinePhotoFilter,
    MdOutlineTune,
    MdOutlineFlip
} from 'react-icons/md';
import { BiReset } from 'react-icons/bi';
import { RiScissorsCutFill } from 'react-icons/ri';

const ImageEditSidebar = () => {
    const { selectedIds, elements, updateElement } = useDesignSpace();

    const [selectedImage, setSelectedImage] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [isRemovingBackground, setIsRemovingBackground] = useState(false);
    const [isManualErasing, setIsManualErasing] = useState(false);
    const [showEraserTool, setShowEraserTool] = useState(false);
    const [eraserSize, setEraserSize] = useState(20);
    const [activeIndex, setActiveIndex] = useState([0]); // Default open first tab
    const [editSettings, setEditSettings] = useState({
        brightness: 100,
        contrast: 100,
        blur: 0,
        opacity: 100,
        rotation: 0,
        flipX: false,
        flipY: false,
        saturation: 100,
        hue: 0,
        sepia: 0,
        grayscale: 0
    });

    // Find the selected image
    useEffect(() => {
        if (selectedIds.length === 1) {
            const element = elements.find(el => el.id === selectedIds[0]);
            if (element && element.type === 'img') {
                setSelectedImage(element);

                // Initialize edit settings from existing filters if any
                if (element.filters) {
                    setEditSettings({
                        ...editSettings,
                        ...element.filters
                    });
                }
            } else {
                setSelectedImage(null);
            }
        } else {
            setSelectedImage(null);
        }
    }, [selectedIds, elements]);

    // Apply filters to the image
    const applyFilters = () => {
        if (!selectedImage) return;

        // Apply filters immediately without showing processing indicator
        const filters = {
            brightness: `brightness(${editSettings.brightness}%)`,
            contrast: `contrast(${editSettings.contrast}%)`,
            blur: `blur(${editSettings.blur}px)`,
            opacity: `opacity(${editSettings.opacity}%)`,
            saturation: `saturate(${editSettings.saturation}%)`,
            hue: `hue-rotate(${editSettings.hue}deg)`,
            sepia: `sepia(${editSettings.sepia}%)`,
            grayscale: `grayscale(${editSettings.grayscale}%)`
        };

        const filterString = Object.values(filters).join(' ');
        const transform = `rotate(${editSettings.rotation}deg) scaleX(${editSettings.flipX ? -1 : 1}) scaleY(${editSettings.flipY ? -1 : 1})`;

        updateElement(selectedImage.id, {
            filters: editSettings,
            style: {
                filter: filterString,
                transform: transform
            }
        });
    };

    // Apply filters when settings change with debounce
    useEffect(() => {
        const debounceTimer = setTimeout(() => {
            applyFilters();
        }, 100); // Small debounce to prevent too many updates

        return () => clearTimeout(debounceTimer);
    }, [editSettings]);

    // Handle slider changes
    const handleSliderChange = (property, value) => {
        setEditSettings(prev => ({
            ...prev,
            [property]: value
        }));
    };

    // Reset all filters
    const resetFilters = () => {
        setEditSettings({
            brightness: 100,
            contrast: 100,
            blur: 0,
            opacity: 100,
            rotation: 0,
            flipX: false,
            flipY: false,
            saturation: 100,
            hue: 0,
            sepia: 0,
            grayscale: 0
        });
    };

    // Reset image to original
    const resetImage = () => {
        if (!selectedImage || !selectedImage.originalSrc) return;

        updateElement(selectedImage.id, {
            src: selectedImage.originalSrc
        });

        setShowEraserTool(false);
    };

    // Start manual erasing mode - Direct image cutting
    const startManualErasing = () => {
        if (!selectedImage || !selectedImage.src) return;

        setIsManualErasing(true);

        // Create a canvas overlay for erasing
        const designSpace = document.querySelector('.design-space-container');
        if (!designSpace) {
            setIsManualErasing(false);
            return;
        }

        // Get the selected image element
        const imageElement = document.querySelector(`[data-element-id="${selectedImage.id}"]`);
        if (!imageElement) {
            setIsManualErasing(false);
            return;
        }

        // Get image position and dimensions
        const imageRect = imageElement.getBoundingClientRect();
        const designSpaceRect = designSpace.getBoundingClientRect();

        // Create canvas overlay
        const overlay = document.createElement('div');
        overlay.className = 'eraser-overlay';
        overlay.style.position = 'absolute';
        overlay.style.left = `${imageRect.left - designSpaceRect.left}px`;
        overlay.style.top = `${imageRect.top - designSpaceRect.top}px`;
        overlay.style.width = `${imageRect.width}px`;
        overlay.style.height = `${imageRect.height}px`;
        overlay.style.zIndex = '9999';
        overlay.style.cursor = 'crosshair';
        overlay.style.border = '2px dashed #4F46E5';
        overlay.style.boxShadow = '0 0 0 2000px rgba(0, 0, 0, 0.5)';

        // Create canvas
        const canvas = document.createElement('canvas');
        canvas.width = imageRect.width;
        canvas.height = imageRect.height;
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        overlay.appendChild(canvas);

        // Add to design space
        designSpace.appendChild(overlay);

        // Add instructions
        const instructions = document.createElement('div');
        instructions.style.position = 'absolute';
        instructions.style.top = '-40px';
        instructions.style.left = '0';
        instructions.style.right = '0';
        instructions.style.textAlign = 'center';
        instructions.style.color = 'white';
        instructions.style.fontWeight = 'bold';
        instructions.style.textShadow = '0 0 5px rgba(0,0,0,0.7)';
        instructions.innerHTML = 'اضغط واسحب لإزالة أجزاء من الصورة';
        overlay.appendChild(instructions);

        // Load image into canvas
        const ctx = canvas.getContext('2d');
        const img = new Image();
        img.crossOrigin = "Anonymous";
        img.src = selectedImage.src;

        img.onload = () => {
            // Draw image on canvas
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

            // Variables for drawing
            let isDrawing = false;
            let lastX = 0;
            let lastY = 0;

            // Start drawing
            overlay.addEventListener('mousedown', startDrawing);
            overlay.addEventListener('mousemove', draw);
            overlay.addEventListener('mouseup', stopDrawing);
            overlay.addEventListener('mouseleave', stopDrawing);

            // Touch events
            overlay.addEventListener('touchstart', handleTouchStart);
            overlay.addEventListener('touchmove', handleTouchMove);
            overlay.addEventListener('touchend', handleTouchEnd);

            function startDrawing(e) {
                isDrawing = true;
                const rect = canvas.getBoundingClientRect();
                lastX = e.clientX - rect.left;
                lastY = e.clientY - rect.top;
            }

            function draw(e) {
                if (!isDrawing) return;

                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Set eraser properties
                ctx.globalCompositeOperation = 'destination-out';
                ctx.lineJoin = 'round';
                ctx.lineCap = 'round';
                ctx.lineWidth = eraserSize;

                // Draw line
                ctx.beginPath();
                ctx.moveTo(lastX, lastY);
                ctx.lineTo(x, y);
                ctx.stroke();

                // Update last position
                lastX = x;
                lastY = y;

                // Update the image with the erased version
                const newImageUrl = canvas.toDataURL('image/png');
                updateElement(selectedImage.id, {
                    src: newImageUrl,
                    originalSrc: selectedImage.originalSrc || selectedImage.src
                });
            }

            function handleTouchStart(e) {
                e.preventDefault();
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent('mousedown', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                overlay.dispatchEvent(mouseEvent);
            }

            function handleTouchMove(e) {
                e.preventDefault();
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                overlay.dispatchEvent(mouseEvent);
            }

            function handleTouchEnd(e) {
                e.preventDefault();
                const mouseEvent = new MouseEvent('mouseup', {});
                overlay.dispatchEvent(mouseEvent);
            }

            function stopDrawing() {
                isDrawing = false;
            }

            // Add control buttons
            const buttonContainer = document.createElement('div');
            buttonContainer.style.position = 'absolute';
            buttonContainer.style.bottom = '-50px';
            buttonContainer.style.left = '0';
            buttonContainer.style.right = '0';
            buttonContainer.style.display = 'flex';
            buttonContainer.style.justifyContent = 'center';
            buttonContainer.style.gap = '10px';

            // Done button
            const doneButton = document.createElement('button');
            doneButton.innerHTML = 'تم';
            doneButton.style.padding = '8px 20px';
            doneButton.style.backgroundColor = '#4F46E5';
            doneButton.style.color = 'white';
            doneButton.style.borderRadius = '4px';
            doneButton.style.border = 'none';
            doneButton.style.cursor = 'pointer';
            doneButton.style.fontWeight = 'bold';

            doneButton.addEventListener('click', () => {
                // Clean up
                overlay.remove();
                setIsManualErasing(false);
            });

            // Reset button
            const resetButton = document.createElement('button');
            resetButton.innerHTML = 'إعادة ضبط';
            resetButton.style.padding = '8px 20px';
            resetButton.style.backgroundColor = '#f3f4f6';
            resetButton.style.color = '#374151';
            resetButton.style.borderRadius = '4px';
            resetButton.style.border = '1px solid #d1d5db';
            resetButton.style.cursor = 'pointer';

            resetButton.addEventListener('click', () => {
                // Redraw original image
                ctx.globalCompositeOperation = 'source-over';
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                // Update the image
                updateElement(selectedImage.id, {
                    src: selectedImage.originalSrc || selectedImage.src
                });
            });

            buttonContainer.appendChild(resetButton);
            buttonContainer.appendChild(doneButton);
            overlay.appendChild(buttonContainer);
        };

        img.onerror = () => {
            overlay.remove();
            setIsManualErasing(false);
            alert('Failed to load image for editing');
        };
    };

    // Apply preset filters
    const applyPreset = (preset) => {
        switch(preset) {
            case 'vintage':
                setEditSettings(prev => ({
                    ...prev,
                    sepia: 50,
                    contrast: 120,
                    brightness: 90,
                    saturation: 85
                }));
                break;
            case 'blackAndWhite':
                setEditSettings(prev => ({
                    ...prev,
                    grayscale: 100,
                    contrast: 120,
                    brightness: 110,
                    saturation: 0
                }));
                break;
            case 'warm':
                setEditSettings(prev => ({
                    ...prev,
                    hue: 30,
                    saturation: 120,
                    brightness: 105,
                    sepia: 20
                }));
                break;
            case 'cool':
                setEditSettings(prev => ({
                    ...prev,
                    hue: 210,
                    saturation: 90,
                    brightness: 105,
                    contrast: 110
                }));
                break;
            case 'sharp':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 130,
                    brightness: 110,
                    saturation: 110
                }));
                break;
            case 'dramatic':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 150,
                    brightness: 90,
                    saturation: 120,
                    hue: 0
                }));
                break;
            case 'faded':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 90,
                    brightness: 110,
                    saturation: 70,
                    opacity: 90
                }));
                break;
            case 'retro':
                setEditSettings(prev => ({
                    ...prev,
                    sepia: 30,
                    hue: 20,
                    saturation: 90,
                    contrast: 110
                }));
                break;
            case 'cinema':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 120,
                    saturation: 85,
                    brightness: 95,
                    sepia: 10
                }));
                break;
            default:
                break;
        }
    };

    // Rotate image
    const rotateImage = (direction) => {
        const newRotation = editSettings.rotation + (direction === 'right' ? 90 : -90);
        setEditSettings(prev => ({
            ...prev,
            rotation: newRotation
        }));
    };

    // Flip image
    const flipImage = (axis) => {
        if (axis === 'horizontal') {
            setEditSettings(prev => ({
                ...prev,
                flipX: !prev.flipX
            }));
        } else {
            setEditSettings(prev => ({
                ...prev,
                flipY: !prev.flipY
            }));
        }
    };

    // Remove background by specific color
    const removeBackgroundByColor = async (r, g, b, tolerance) => {
        if (!selectedImage || !selectedImage.src) return;

        setIsRemovingBackground(true);

        try {
            // Get the image data
            const imageUrl = selectedImage.src;

            // Create a new image with transparent background
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.src = imageUrl;

            await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
            });

            // Create canvas to process the image
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');

            // Draw the image on the canvas
            ctx.drawImage(img, 0, 0);

            // Get image data
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Convert tolerance from percentage to color difference value (0-255)
            const colorTolerance = (tolerance / 100) * 255;

            // Loop through all pixels
            for (let i = 0; i < data.length; i += 4) {
                const pixelR = data[i];
                const pixelG = data[i + 1];
                const pixelB = data[i + 2];

                // Calculate color difference
                const colorDiff = Math.sqrt(
                    Math.pow(pixelR - r, 2) +
                    Math.pow(pixelG - g, 2) +
                    Math.pow(pixelB - b, 2)
                );

                // If color is within tolerance, make it transparent
                if (colorDiff < colorTolerance) {
                    data[i + 3] = 0; // Set alpha to 0 (transparent)
                }
            }

            // Put the modified image data back on the canvas
            ctx.putImageData(imageData, 0, 0);

            // Convert canvas to data URL
            const newImageUrl = canvas.toDataURL('image/png');

            // Update the image element with the new image
            updateElement(selectedImage.id, {
                src: newImageUrl,
                originalSrc: selectedImage.originalSrc || selectedImage.src // Keep track of the original image
            });

            console.log("Background color removal completed successfully");

        } catch (error) {
            console.error('Error removing background color:', error);
            // Show error message to user
            alert('Failed to remove background. Please try again.');
        } finally {
            setIsRemovingBackground(false);
        }
    };

    // Remove background from image using a more direct approach
    const removeBackground = async () => {
        if (!selectedImage || !selectedImage.src) return;

        setIsRemovingBackground(true);

        try {
            // Get the image data
            const imageUrl = selectedImage.src;

            // Create a new image with transparent background
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.src = imageUrl;

            await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
            });

            // Create canvas to process the image
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');

            // Draw the image on the canvas
            ctx.drawImage(img, 0, 0);

            // Create a new canvas for the output with transparency
            const outputCanvas = document.createElement('canvas');
            outputCanvas.width = img.width;
            outputCanvas.height = img.height;
            const outputCtx = outputCanvas.getContext('2d');

            // Get image data
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            const width = canvas.width;
            const height = canvas.height;

            // Create a mask for the foreground (1 = foreground, 0 = background)
            const mask = new Uint8Array(width * height);

            // Get the background color from the corners and edges
            const topLeft = getPixelColor(data, 0, 0, width);
            const topRight = getPixelColor(data, width - 1, 0, width);
            const bottomLeft = getPixelColor(data, 0, height - 1, width);
            const bottomRight = getPixelColor(data, width - 1, height - 1, width);

            // Calculate average background color
            const avgBgColor = {
                r: Math.round((topLeft.r + topRight.r + bottomLeft.r + bottomRight.r) / 4),
                g: Math.round((topLeft.g + topRight.g + bottomLeft.g + bottomRight.g) / 4),
                b: Math.round((topLeft.b + topRight.b + bottomLeft.b + bottomRight.b) / 4)
            };

            // Threshold for color similarity - more aggressive for better results
            const threshold = 40;

            // Mark background pixels in the mask
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const idx = (y * width + x) * 4;
                    const r = data[idx];
                    const g = data[idx + 1];
                    const b = data[idx + 2];

                    // Calculate color difference from background
                    const colorDiff = Math.sqrt(
                        Math.pow(r - avgBgColor.r, 2) +
                        Math.pow(g - avgBgColor.g, 2) +
                        Math.pow(b - avgBgColor.b, 2)
                    );

                    // If color is similar to background, mark as background (0)
                    if (colorDiff < threshold) {
                        mask[y * width + x] = 0; // Background
                    } else {
                        mask[y * width + x] = 1; // Foreground
                    }
                }
            }

            // Flood fill from the edges to improve background detection
            floodFillFromEdges(mask, width, height);

            // Create a new image with transparent background
            const outputData = outputCtx.createImageData(width, height);
            const outputPixels = outputData.data;

            // Copy the original image but make background transparent
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const idx = (y * width + x) * 4;
                    const maskIdx = y * width + x;

                    // Copy RGB values
                    outputPixels[idx] = data[idx];         // R
                    outputPixels[idx + 1] = data[idx + 1]; // G
                    outputPixels[idx + 2] = data[idx + 2]; // B

                    // Set alpha based on mask (0 = transparent, 255 = opaque)
                    outputPixels[idx + 3] = mask[maskIdx] ? 255 : 0;
                }
            }

            // Put the modified image data on the output canvas
            outputCtx.putImageData(outputData, 0, 0);

            // Convert canvas to data URL
            const newImageUrl = outputCanvas.toDataURL('image/png');

            // Update the image element with the new image
            updateElement(selectedImage.id, {
                src: newImageUrl,
                originalSrc: selectedImage.originalSrc || selectedImage.src // Keep track of the original image
            });

            console.log("Background removal completed successfully");

        } catch (error) {
            console.error('Error removing background:', error);
            // Show error message to user
            alert('Failed to remove background. Please try again.');
        } finally {
            setIsRemovingBackground(false);
        }
    };

    // Helper function to get pixel color at (x,y)
    const getPixelColor = (data, x, y, width) => {
        const idx = (y * width + x) * 4;
        return {
            r: data[idx],
            g: data[idx + 1],
            b: data[idx + 2]
        };
    };

    // Helper function to flood fill from edges to improve background detection
    const floodFillFromEdges = (mask, width, height) => {
        // Queue for flood fill
        const queue = [];

        // Start from the edges
        for (let x = 0; x < width; x++) {
            if (mask[x] === 1) { queue.push([x, 0]); mask[x] = 0; }
            if (mask[(height-1) * width + x] === 1) { queue.push([x, height-1]); mask[(height-1) * width + x] = 0; }
        }

        for (let y = 0; y < height; y++) {
            if (mask[y * width] === 1) { queue.push([0, y]); mask[y * width] = 0; }
            if (mask[y * width + (width-1)] === 1) { queue.push([width-1, y]); mask[y * width + (width-1)] = 0; }
        }

        // Directions for 4-connected neighbors
        const dx = [0, 1, 0, -1];
        const dy = [-1, 0, 1, 0];

        // Process the queue
        while (queue.length > 0) {
            const [x, y] = queue.shift();

            // Check all 4 neighbors
            for (let i = 0; i < 4; i++) {
                const nx = x + dx[i];
                const ny = y + dy[i];

                // Check if neighbor is valid
                if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                    const idx = ny * width + nx;
                    if (mask[idx] === 1) {
                        mask[idx] = 0; // Mark as background
                        queue.push([nx, ny]);
                    }
                }
            }
        }

        // Invert the mask (now 1 = foreground, 0 = background)
        for (let i = 0; i < mask.length; i++) {
            mask[i] = mask[i] === 0 ? 0 : 1;
        }
    };

    // Remove background by specific color (second definition - delete this)
    const removeBackgroundByColorDuplicate = async (r, g, b, tolerance) => {
        if (!selectedImage || !selectedImage.src) return;

        setIsRemovingBackground(true);

        try {
            // Get the image data
            const imageUrl = selectedImage.src;

            // Create a new image with transparent background
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.src = imageUrl;

            await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
            });

            // Create canvas to process the image
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');

            // Draw the image on the canvas
            ctx.drawImage(img, 0, 0);

            // Get image data
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Convert tolerance from percentage to color difference value (0-255)
            const colorTolerance = (tolerance / 100) * 255;

            // Loop through all pixels
            for (let i = 0; i < data.length; i += 4) {
                const pixelR = data[i];
                const pixelG = data[i + 1];
                const pixelB = data[i + 2];

                // Calculate color difference
                const colorDiff = Math.sqrt(
                    Math.pow(pixelR - r, 2) +
                    Math.pow(pixelG - g, 2) +
                    Math.pow(pixelB - b, 2)
                );

                // If color is within tolerance, make it transparent
                if (colorDiff < colorTolerance) {
                    data[i + 3] = 0; // Set alpha to 0 (transparent)
                }
            }

            // Put the modified image data back on the canvas
            ctx.putImageData(imageData, 0, 0);

            // Convert canvas to data URL
            const newImageUrl = canvas.toDataURL('image/png');

            // Update the image element with the new image
            updateElement(selectedImage.id, {
                src: newImageUrl,
                originalSrc: selectedImage.originalSrc || selectedImage.src // Keep track of the original image
            });

            console.log("Background color removal completed successfully");

        } catch (error) {
            console.error('Error removing background color:', error);
            // Show error message to user
            alert('Failed to remove background. Please try again.');
        } finally {
            setIsRemovingBackground(false);
        }
    };

    if (!selectedImage) {
        return (
            <div className="image-edit-sidebar bg-white p-4 border-l border-gray-200 h-full">
                <div className="text-center text-gray-500 py-8">
                    <MdOutlinePhotoFilter size={48} className="mx-auto mb-4 text-gray-300" />
                    <p>Select an image to edit</p>
                </div>
            </div>
        );
    }

    return (
        <div className="image-edit-sidebar bg-white h-full" style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
            <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white z-10" style={{ flexShrink: 0 }}>
                <h3 className="text-lg font-medium">Image Editor</h3>
                <div className="flex items-center">
                    <Button
                        icon="pi pi-undo"
                        className="p-button-rounded p-button-text p-button-sm mr-2"
                        onClick={resetFilters}
                        tooltip="Reset All"
                        tooltipOptions={{ position: 'bottom' }}
                    />
                </div>
            </div>

            {/* Background Removal Options */}
            {/* <div className="px-4 py-3 border-b border-gray-200 bg-white" style={{ flexShrink: 0 }}>
                <div className="bg-removal-tools">
                    <h4 className="text-sm font-medium mb-3">إزالة خلفية الصورة</h4>

                    <div className="mb-4">
                        <button
                            className={`w-full p-3 rounded-lg flex items-center justify-center ${
                                isRemovingBackground
                                    ? 'bg-purple-100 text-purple-600'
                                    : 'bg-gradient-to-r from-red-500 to-orange-500 text-white hover:from-red-600 hover:to-orange-600'
                            }`}
                            onClick={removeBackground}
                            disabled={isRemovingBackground}
                        >
                            <RiScissorsCutFill className="mr-2" size={20} />
                            {isRemovingBackground ? 'جاري المعالجة...' : 'إزالة الخلفية تلقائياً'}
                        </button>

                        {isRemovingBackground && (
                            <div className="mt-2">
                                <ProgressBar mode="indeterminate" style={{ height: '6px' }} />
                                <p className="text-xs text-center mt-1 text-gray-500">جاري معالجة الصورة، يرجى الانتظار...</p>
                            </div>
                        )}
                    </div>

                    <div className="relative border-t border-gray-200 pt-4 mt-4">
                        <h4 className="text-sm font-medium mb-3">تحديد لون الخلفية لإزالته</h4>

                        <div className="mb-4">
                            <div className="bg-gray-100 rounded-lg p-3 mb-3">
                                <p className="text-sm text-gray-700 mb-2">
                                    حدد لون الخلفية الذي تريد إزالته من الصورة
                                </p>

                                <div className="flex items-center space-x-2 mb-3">
                                    <input
                                        type="color"
                                        value="#ffffff"
                                        className="w-10 h-10 rounded cursor-pointer"
                                        onChange={(e) => {
                                            // Store selected color for background removal
                                            window.selectedBgColor = e.target.value;
                                        }}
                                    />
                                    <span className="text-sm">← اختر لون الخلفية</span>
                                </div>

                                <div className="flex justify-between items-center mb-2">
                                    <label className="text-sm font-medium">دقة الإزالة: {eraserSize}%</label>
                                </div>
                                <Slider
                                    value={eraserSize}
                                    onChange={(e) => setEraserSize(e.value)}
                                    min={1}
                                    max={100}
                                />
                            </div>

                            <button
                                className="w-full p-3 rounded-lg flex items-center justify-center bg-blue-600 text-white hover:bg-blue-700"
                                onClick={() => {
                                    // Get the selected color
                                    const selectedColor = window.selectedBgColor || '#ffffff';

                                    // Convert hex to RGB
                                    const r = parseInt(selectedColor.slice(1, 3), 16);
                                    const g = parseInt(selectedColor.slice(3, 5), 16);
                                    const b = parseInt(selectedColor.slice(5, 7), 16);

                                    // Remove background with selected color
                                    removeBackgroundByColorDuplicate(r, g, b, eraserSize);
                                }}
                                disabled={isRemovingBackground}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
                                </svg>
                                إزالة اللون المحدد
                            </button>
                        </div>

                        {selectedImage && selectedImage.originalSrc && (
                            <button
                                className="w-full p-2 rounded-lg flex items-center justify-center bg-gray-200 text-gray-700 hover:bg-gray-300"
                                onClick={resetImage}
                            >
                                <BiReset className="mr-2" size={16} />
                                استعادة الصورة الأصلية
                            </button>
                        )}

                        <div className="mt-3 p-2 bg-blue-50 rounded-lg border border-blue-100">
                            <p className="text-xs text-center text-blue-700">
                                اختر لون الخلفية الذي تريد إزالته، ثم اضبط دقة الإزالة واضغط على الزر
                            </p>
                        </div>
                    </div>
                </div>
            </div> */}

            {/* Processing indicator removed to fix jitter */}

            <div style={{
                flexGrow: 1,
                overflowY: 'auto',
                padding: '16px',
                height: '100%',
                position: 'relative'
            }}>
                <div style={{ position: 'absolute', width: '100%', left: 0, padding: '0 16px' }}>
                    <Accordion multiple activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                <AccordionTab header="Transform">
                    <div className="mb-4">
                        <div className="flex space-x-2 mb-3">
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => rotateImage('left')}
                                title="Rotate Left"
                            >
                                <FiRotateCcw className="mr-2" />
                                <span>Rotate Left</span>
                            </button>
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => rotateImage('right')}
                                title="Rotate Right"
                            >
                                <FiRotateCw className="mr-2" />
                                <span>Rotate Right</span>
                            </button>
                        </div>
                        <div className="flex space-x-2">
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => flipImage('horizontal')}
                                title="Flip Horizontal"
                            >
                                <MdOutlineFlip className="mr-2 rotate-90" />
                                <span>Flip H</span>
                            </button>
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => flipImage('vertical')}
                                title="Flip Vertical"
                            >
                                <MdOutlineFlip className="mr-2" />
                                <span>Flip V</span>
                            </button>
                        </div>
                    </div>
                </AccordionTab>

                <AccordionTab header="Adjustments">
                    <div className="space-y-4">
                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineBrightness6 className="mr-2 text-blue-600" />
                                    <label className="text-sm">Brightness</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.brightness}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.brightness}
                                onChange={(e) => handleSliderChange('brightness', e.value)}
                                min={0}
                                max={200}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>100%</span>
                                <span>200%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineContrast className="mr-2 text-purple-600" />
                                    <label className="text-sm">Contrast</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.contrast}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.contrast}
                                onChange={(e) => handleSliderChange('contrast', e.value)}
                                min={0}
                                max={200}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>100%</span>
                                <span>200%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineBlurOn className="mr-2 text-indigo-600" />
                                    <label className="text-sm">Blur</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.blur}px
                                </div>
                            </div>
                            <Slider
                                value={editSettings.blur}
                                onChange={(e) => handleSliderChange('blur', e.value)}
                                min={0}
                                max={10}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0px</span>
                                <span>5px</span>
                                <span>10px</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineOpacity className="mr-2 text-gray-600" />
                                    <label className="text-sm">Opacity</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.opacity}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.opacity}
                                onChange={(e) => handleSliderChange('opacity', e.value)}
                                min={0}
                                max={100}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>50%</span>
                                <span>100%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineColorLens className="mr-2 text-pink-600" />
                                    <label className="text-sm">Saturation</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.saturation}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.saturation}
                                onChange={(e) => handleSliderChange('saturation', e.value)}
                                min={0}
                                max={200}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>100%</span>
                                <span>200%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineTune className="mr-2 text-green-600" />
                                    <label className="text-sm">Hue Rotate</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.hue}°
                                </div>
                            </div>
                            <Slider
                                value={editSettings.hue}
                                onChange={(e) => handleSliderChange('hue', e.value)}
                                min={0}
                                max={360}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0°</span>
                                <span>180°</span>
                                <span>360°</span>
                            </div>
                        </div>
                    </div>
                </AccordionTab>

                <AccordionTab header="Filters">
                    <div className="grid grid-cols-3 gap-2">
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('vintage')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-amber-100 border border-gray-200 flex items-center justify-center text-amber-800 font-bold">V</div>
                            <span className="text-xs">Vintage</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('blackAndWhite')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-gray-800 border border-gray-200 flex items-center justify-center text-white font-bold">B&W</div>
                            <span className="text-xs">B&W</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('warm')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-orange-100 border border-gray-200 flex items-center justify-center text-orange-600 font-bold">W</div>
                            <span className="text-xs">Warm</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('cool')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-blue-100 border border-gray-200 flex items-center justify-center text-blue-600 font-bold">C</div>
                            <span className="text-xs">Cool</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('sharp')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-gray-100 border border-gray-200 flex items-center justify-center text-gray-800 font-bold">S</div>
                            <span className="text-xs">Sharp</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('dramatic')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-purple-100 border border-gray-200 flex items-center justify-center text-purple-800 font-bold">D</div>
                            <span className="text-xs">Dramatic</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('faded')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-gray-200 border border-gray-200 flex items-center justify-center text-gray-500 font-bold">F</div>
                            <span className="text-xs">Faded</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('retro')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-yellow-100 border border-gray-200 flex items-center justify-center text-yellow-800 font-bold">R</div>
                            <span className="text-xs">Retro</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('cinema')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-indigo-100 border border-gray-200 flex items-center justify-center text-indigo-800 font-bold">C</div>
                            <span className="text-xs">Cinema</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={resetFilters}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-white border border-gray-200 flex items-center justify-center text-gray-800 font-bold">N</div>
                            <span className="text-xs">Normal</span>
                        </div>
                    </div>
                </AccordionTab>
            </Accordion>
                </div>
            </div>
        </div>
    );
};

export default ImageEditSidebar;
