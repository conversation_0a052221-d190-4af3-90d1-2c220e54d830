import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, TabPanel } from 'primereact/tabview';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Slider } from 'primereact/slider';
import { Message } from 'primereact/message';
import { ProgressBar } from 'primereact/progressbar';
import { Chip } from 'primereact/chip';
import { Divider } from 'primereact/divider';
import { useDesignSpace } from '@contexts/DesignSpaceContext';

// Icons
import { FaRobot, FaMagic, FaImage, FaFont, FaPalette, FaPhotoVideo } from 'react-icons/fa';
import { BiPaint, BiText, BiShapeSquare, BiPencil } from 'react-icons/bi';
import { MdOutlineAutoFixHigh, MdOutlinePhotoFilter, MdOutlineColorLens, MdOutlineStyle, MdOutlineTextFields, MdFilterVintage, MdTune, MdOutlineAutoAwesome, MdSettings, MdPhotoSizeSelectLarge } from 'react-icons/md';
import { RiAiGenerate, RiMagicLine, RiLayoutMasonryLine } from 'react-icons/ri';
import { TbBrandOpenai, TbAdjustments, TbColorFilter } from 'react-icons/tb';
import { IoColorPaletteOutline } from 'react-icons/io5';

// Import the ImageTools component
import ImageTools from './ImageTools';

const AIToolsHub = () => {
    // Get design space context
    const {
        addElement,
        setSelectedIds,
        selectedIds,
        elements,
        updateElement,
        setElements
    } = useDesignSpace();

    const [activeTab, setActiveTab] = useState(0);
    const [activeSubTab, setActiveSubTab] = useState(0);
    const [isProcessing, setIsProcessing] = useState(false);
    const [resultMessage, setResultMessage] = useState(null);

    // Get the currently selected image (if any)
    const selectedImage = React.useMemo(() => {
        if (selectedIds.length === 1) {
            const selectedElement = elements.find(el => el.id === selectedIds[0]);
            if (selectedElement && selectedElement.type === 'img') {
                return selectedElement;
            }
        }
        return null;
    }, [selectedIds, elements]);

    // Image generation and editing states
    const [imagePrompt, setImagePrompt] = useState('');
    const [generatedImage, setGeneratedImage] = useState(null);
    const [imageEditMode, setImageEditMode] = useState('generate'); // generate, enhance, style, background, crop, filters, adjust, effects
    const [imageStyle, setImageStyle] = useState('realistic');
    const [imageEnhanceLevel, setImageEnhanceLevel] = useState(50);
    const [imageBackgroundPrompt, setImageBackgroundPrompt] = useState('');
    const [imageEditedVersion, setImageEditedVersion] = useState(null);

    // New image editing states
    const [cropRatio, setCropRatio] = useState('free');
    const [cropRotation, setCropRotation] = useState(0);
    const [brightness, setBrightness] = useState(100);
    const [contrast, setContrast] = useState(100);
    const [saturation, setSaturation] = useState(100);
    const [blur, setBlur] = useState(0);
    const [sharpen, setSharpen] = useState(0);
    const [hue, setHue] = useState(0);
    const [opacity, setOpacity] = useState(100);
    const [selectedFilter, setSelectedFilter] = useState('none');
    const [effectType, setEffectType] = useState('none');
    const [effectIntensity, setEffectIntensity] = useState(50);

    // Text styling states
    const [textContent, setTextContent] = useState('Welcome to our design platform');
    const [fontSize, setFontSize] = useState(16);
    const [fontFamily, setFontFamily] = useState('Arial, sans-serif');
    const [fontWeight, setFontWeight] = useState('normal');
    const [textColor, setTextColor] = useState('#000000');
    const [backgroundColor, setBackgroundColor] = useState('transparent');
    const [textAlign, setTextAlign] = useState('left');
    const [lineHeight, setLineHeight] = useState(1.5);
    const [letterSpacing, setLetterSpacing] = useState(0);
    const [textDecoration, setTextDecoration] = useState('none');
    const [textTransform, setTextTransform] = useState('none');
    const [textEffect, setTextEffect] = useState('none');
    const [textStyle, setTextStyle] = useState(null);
    const [textOpacity, setTextOpacity] = useState(100);
    const [textRotation, setTextRotation] = useState(0);
    const [textShadowColor, setTextShadowColor] = useState('#000000');
    const [textShadowBlur, setTextShadowBlur] = useState(2);
    const [textShadowOffset, setTextShadowOffset] = useState(2);

    // Color palette generation states
    const [baseColor, setBaseColor] = useState('#6366F1');
    const [paletteType, setPaletteType] = useState('complementary');
    const [generatedPalette, setGeneratedPalette] = useState(null);

    // Text style options
    const textStyleOptions = [
        { label: 'Professional', value: 'professional' },
        { label: 'Casual', value: 'casual' },
        { label: 'Creative', value: 'creative' },
        { label: 'Formal', value: 'formal' },
        { label: 'Persuasive', value: 'persuasive' }
    ];

    // Font family options
    const fontFamilyOptions = [
        { label: 'Arial', value: 'Arial, sans-serif' },
        { label: 'Helvetica', value: 'Helvetica, sans-serif' },
        { label: 'Times New Roman', value: 'Times New Roman, serif' },
        { label: 'Georgia', value: 'Georgia, serif' },
        { label: 'Verdana', value: 'Verdana, sans-serif' },
        { label: 'Courier New', value: 'Courier New, monospace' },
        { label: 'Tahoma', value: 'Tahoma, sans-serif' },
        { label: 'Trebuchet MS', value: 'Trebuchet MS, sans-serif' },
        { label: 'Impact', value: 'Impact, sans-serif' },
        { label: 'Comic Sans MS', value: 'Comic Sans MS, cursive' }
    ];

    // Font weight options
    const fontWeightOptions = [
        { label: 'Normal', value: 'normal' },
        { label: 'Bold', value: 'bold' },
        { label: 'Light', value: '300' },
        { label: 'Semi-Bold', value: '600' },
        { label: 'Extra Bold', value: '800' }
    ];

    // Text alignment options
    const textAlignOptions = [
        { label: 'Left', value: 'left' },
        { label: 'Center', value: 'center' },
        { label: 'Right', value: 'right' },
        { label: 'Justify', value: 'justify' }
    ];

    // Text decoration options
    const textDecorationOptions = [
        { label: 'None', value: 'none' },
        { label: 'Underline', value: 'underline' },
        { label: 'Overline', value: 'overline' },
        { label: 'Line-through', value: 'line-through' }
    ];

    // Text transform options
    const textTransformOptions = [
        { label: 'None', value: 'none' },
        { label: 'Uppercase', value: 'uppercase' },
        { label: 'Lowercase', value: 'lowercase' },
        { label: 'Capitalize', value: 'capitalize' }
    ];

    // Text effect options
    const textEffectOptions = [
        { label: 'None', value: 'none' },
        { label: 'Shadow', value: 'shadow' },
        { label: 'Glow', value: 'glow' },
        { label: '3D', value: '3d' },
        { label: 'Outline', value: 'outline' },
        { label: 'Gradient', value: 'gradient' }
    ];

    // Palette type options
    const paletteTypeOptions = [
        { label: 'Complementary', value: 'complementary' },
        { label: 'Analogous', value: 'analogous' },
        { label: 'Triadic', value: 'triadic' },
        { label: 'Monochromatic', value: 'monochromatic' },
        { label: 'Split Complementary', value: 'split-complementary' }
    ];

    // Sample images for different categories
    const sampleImages = {
        'landscape': 'https://img.freepik.com/free-photo/painting-mountain-lake-with-mountain-background_188544-9126.jpg',
        'portrait': 'https://img.freepik.com/free-photo/young-beautiful-woman-pink-warm-sweater-natural-look-smiling-portrait-isolated-long-hair_285396-896.jpg',
        'abstract': 'https://img.freepik.com/free-photo/abstract-colorful-splash-3d-background-generative-ai-background_60438-2509.jpg',
        'nature': 'https://img.freepik.com/free-photo/beautiful-shot-forest-with-tall-green-trees_181624-20615.jpg',
        'city': 'https://img.freepik.com/free-photo/aerial-shot-manhattan-cityscape-with-tall-skyscrapers_181624-5066.jpg',
        'food': 'https://img.freepik.com/free-photo/top-view-table-full-delicious-food-composition_23-2149141352.jpg',
        'animal': 'https://img.freepik.com/free-photo/view-wild-lion-nature_23-2150460851.jpg',
        'sunset': 'https://img.freepik.com/free-photo/beautiful-sunset-beach-tropical-sea_74190-6102.jpg',
        'beach': 'https://img.freepik.com/free-photo/tropical-beach_74190-188.jpg',
        'mountain': 'https://img.freepik.com/free-photo/beautiful-shot-mountains-trees-covered-fog_181624-24505.jpg',
        'default': 'https://img.freepik.com/free-photo/painting-mountain-lake-with-mountain-background_188544-9126.jpg'
    };

    // Image style filters
    const imageStyles = {
        'realistic': { filter: 'none', label: 'Realistic' },
        'watercolor': { filter: 'saturate(1.3) contrast(0.8) brightness(1.1) blur(0.5px)', label: 'Watercolor' },
        'oil-painting': { filter: 'saturate(1.5) contrast(1.2) brightness(0.9)', label: 'Oil Painting' },
        'sketch': { filter: 'grayscale(1) contrast(1.5) brightness(1.2)', label: 'Sketch' },
        'vintage': { filter: 'sepia(0.5) contrast(0.9) brightness(0.9)', label: 'Vintage' },
        'noir': { filter: 'grayscale(1) contrast(1.3) brightness(0.8)', label: 'Noir' },
        'pop-art': { filter: 'saturate(2) contrast(1.5) brightness(1.2) hue-rotate(5deg)', label: 'Pop Art' },
        'comic': { filter: 'saturate(1.7) contrast(1.8) brightness(1.1)', label: 'Comic' },
        'cyberpunk': { filter: 'saturate(1.5) contrast(1.2) brightness(1.1) hue-rotate(270deg)', label: 'Cyberpunk' },
        'fantasy': { filter: 'saturate(1.4) contrast(1.1) brightness(1.1) hue-rotate(15deg)', label: 'Fantasy' },
        'duotone': { filter: 'grayscale(1) sepia(0.8) hue-rotate(180deg)', label: 'Duotone' },
        'neon': { filter: 'brightness(1.2) contrast(1.5) saturate(2) hue-rotate(300deg)', label: 'Neon' },
        'pastel': { filter: 'brightness(1.1) contrast(0.8) saturate(0.7)', label: 'Pastel' },
        'dramatic': { filter: 'contrast(1.5) brightness(0.9) saturate(1.3)', label: 'Dramatic' },
        'cinematic': { filter: 'contrast(1.2) brightness(0.9) saturate(1.1) sepia(0.2)', label: 'Cinematic' }
    };

    // Image filters
    const imageFilters = {
        'none': { filter: 'none', label: 'None' },
        'grayscale': { filter: 'grayscale(1)', label: 'Grayscale' },
        'sepia': { filter: 'sepia(1)', label: 'Sepia' },
        'invert': { filter: 'invert(1)', label: 'Invert' },
        'blur': { filter: 'blur(5px)', label: 'Blur' },
        'sharpen': { filter: 'contrast(1.5) brightness(0.9)', label: 'Sharpen' },
        'hue-rotate-90': { filter: 'hue-rotate(90deg)', label: 'Hue Rotate 90°' },
        'hue-rotate-180': { filter: 'hue-rotate(180deg)', label: 'Hue Rotate 180°' },
        'hue-rotate-270': { filter: 'hue-rotate(270deg)', label: 'Hue Rotate 270°' },
        'saturate': { filter: 'saturate(2)', label: 'Saturate' },
        'desaturate': { filter: 'saturate(0.5)', label: 'Desaturate' }
    };

    // Image effects
    const imageEffects = {
        'none': { filter: 'none', label: 'None' },
        'shadow': { filter: 'drop-shadow(5px 5px 5px rgba(0,0,0,0.5))', label: 'Shadow' },
        'glow': { filter: 'drop-shadow(0px 0px 8px rgba(255,255,255,0.8))', label: 'Glow' },
        'vignette': { filter: 'brightness(1.1) contrast(1.1)', label: 'Vignette', className: 'vignette-effect' },
        'frame': { filter: 'none', label: 'Frame', className: 'frame-effect' },
        'polaroid': { filter: 'saturate(1.3) contrast(1.1) brightness(1.1)', label: 'Polaroid', className: 'polaroid-effect' },
        'old-photo': { filter: 'sepia(0.8) contrast(0.9) brightness(0.9)', label: 'Old Photo', className: 'old-photo-effect' },
        'film-grain': { filter: 'contrast(1.1)', label: 'Film Grain', className: 'film-grain-effect' },
        'color-overlay': { filter: 'brightness(0.9)', label: 'Color Overlay', className: 'color-overlay-effect' }
    };

    // Crop ratio options
    const cropRatioOptions = [
        { label: 'Free', value: 'free' },
        { label: '1:1 (Square)', value: '1:1' },
        { label: '4:3', value: '4:3' },
        { label: '16:9', value: '16:9' },
        { label: '3:2', value: '3:2' },
        { label: '2:3', value: '2:3' },
        { label: '9:16', value: '9:16' }
    ];

    // Generate image based on prompt
    const handleGenerateImage = () => {
        setIsProcessing(true);

        // Simulate API call
        setTimeout(() => {
            let imageUrl = sampleImages.default;

            // Check for keywords in prompt
            const promptLower = imagePrompt.toLowerCase();
            for (const [category, url] of Object.entries(sampleImages)) {
                if (promptLower.includes(category)) {
                    imageUrl = url;
                    break;
                }
            }

            setGeneratedImage(imageUrl);
            setIsProcessing(false);

            setResultMessage({
                severity: 'success',
                summary: 'Success',
                detail: 'Image generated successfully'
            });
        }, 1500);
    };

    // Function to generate text
    const handleGenerateText = () => {
        // Make sure we have a prompt and style
        if (!textPrompt) {
            setResultMessage({
                severity: 'warn',
                summary: 'Warning',
                detail: 'Please enter a text prompt'
            });
            return;
        }

        // Default to 'formal' if no style selected
        const effectiveStyle = textStyle || 'formal';

        setIsProcessing(true);
        setResultMessage({
            severity: 'info',
            summary: 'Processing',
            detail: 'Generating text...'
        });

        // Simple timeout to simulate processing
        setTimeout(() => {
            try {
                // Generate text based on prompt and style
                let result = '';

                if (textPrompt.toLowerCase().includes('greeting')) {
                    result = effectiveStyle === 'formal'
                        ? 'Dear valued customer, we extend our warmest greetings and appreciation for your continued support.'
                        : 'Hey there! Thanks so much for being an awesome customer!';
                } else if (textPrompt.toLowerCase().includes('invitation')) {
                    result = effectiveStyle === 'formal'
                        ? 'We cordially invite you to attend our annual gala dinner on Saturday, June 15th at 7:00 PM.'
                        : 'Join us for an amazing party this Saturday at 7 PM! Food, drinks, and great vibes guaranteed!';
                } else if (textPrompt.toLowerCase().includes('thank')) {
                    result = effectiveStyle === 'formal'
                        ? 'We sincerely thank you for your business and look forward to serving you again in the future.'
                        : "Thanks a million for your support! We're super excited to work with you again soon!";
                } else {
                    result = effectiveStyle === 'formal'
                        ? 'We are pleased to inform you that our new collection has arrived. Please visit our store to explore the latest offerings.'
                        : 'Guess what? Our awesome new collection just dropped! Come check out all the cool new stuff in store now!';
                }

                // Adjust length
                if (textLength < 50) {
                    result = result.split('.')[0] + '.';
                } else if (textLength > 150) {
                    result = result + ' ' + result;
                }

                console.log("Generated text:", result);

                // Set the generated text directly
                setGeneratedText(result);

                setResultMessage({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Text generated successfully'
                });
            } catch (error) {
                console.error('Error generating text:', error);
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to generate text'
                });
            } finally {
                setIsProcessing(false);
            }
        }, 1000);
    };

    // Function to generate color palette
    const handleGeneratePalette = () => {
        setIsProcessing(true);
        setResultMessage({
            severity: 'info',
            summary: 'Processing',
            detail: 'Generating color palette...'
        });

        // Simple timeout to simulate processing
        setTimeout(() => {
            try {
                // Convert hex to RGB
                const hexToRgb = (hex) => {
                    const cleanHex = hex.replace(/^#/, '');
                    const r = parseInt(cleanHex.substring(0, 2), 16);
                    const g = parseInt(cleanHex.substring(2, 4), 16);
                    const b = parseInt(cleanHex.substring(4, 6), 16);
                    return { r, g, b };
                };

                // RGB to Hex
                const rgbToHex = (r, g, b) => {
                    return "#" + ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1);
                };

                // Generate palette based on type
                const baseRgb = hexToRgb(baseColor);
                let palette = [baseColor];

                // Simple complementary color
                if (paletteType === 'complementary') {
                    // Complementary color
                    const r = 255 - baseRgb.r;
                    const g = 255 - baseRgb.g;
                    const b = 255 - baseRgb.b;
                    palette.push(rgbToHex(r, g, b));

                    // Add a few more colors for a complete palette
                    palette.push(rgbToHex(
                        Math.floor((baseRgb.r + r) / 2),
                        Math.floor((baseRgb.g + g) / 2),
                        Math.floor((baseRgb.b + b) / 2)
                    ));

                    // Add a lighter version of the base color
                    palette.push(rgbToHex(
                        Math.min(255, Math.floor(baseRgb.r * 1.3)),
                        Math.min(255, Math.floor(baseRgb.g * 1.3)),
                        Math.min(255, Math.floor(baseRgb.b * 1.3))
                    ));

                    // Add a darker version of the base color
                    palette.push(rgbToHex(
                        Math.floor(baseRgb.r * 0.7),
                        Math.floor(baseRgb.g * 0.7),
                        Math.floor(baseRgb.b * 0.7)
                    ));
                }
                // Simple analogous colors
                else if (paletteType === 'analogous') {
                    // Just create some simple variations
                    palette.push(rgbToHex(
                        (baseRgb.r + 40) % 255,
                        (baseRgb.g + 20) % 255,
                        baseRgb.b
                    ));

                    palette.push(rgbToHex(
                        (baseRgb.r + 80) % 255,
                        (baseRgb.g + 40) % 255,
                        baseRgb.b
                    ));

                    palette.push(rgbToHex(
                        (baseRgb.r - 40 + 255) % 255,
                        (baseRgb.g - 20 + 255) % 255,
                        baseRgb.b
                    ));

                    palette.push(rgbToHex(
                        (baseRgb.r - 80 + 255) % 255,
                        (baseRgb.g - 40 + 255) % 255,
                        baseRgb.b
                    ));
                }
                // Simple monochromatic variations
                else if (paletteType === 'monochromatic') {
                    // Generate monochromatic variations (different lightness)
                    for (let i = 1; i <= 4; i++) {
                        const factor = 0.5 + (i * 0.1);
                        palette.push(rgbToHex(
                            Math.min(255, Math.floor(baseRgb.r * factor)),
                            Math.min(255, Math.floor(baseRgb.g * factor)),
                            Math.min(255, Math.floor(baseRgb.b * factor))
                        ));
                    }
                }
                // For other types, just generate some variations
                else {
                    for (let i = 1; i <= 4; i++) {
                        const r = (baseRgb.r + i * 50) % 255;
                        const g = (baseRgb.g + i * 30) % 255;
                        const b = (baseRgb.b + i * 70) % 255;
                        palette.push(rgbToHex(r, g, b));
                    }
                }

                console.log("Generated palette:", palette);

                // Set the generated palette directly
                setGeneratedPalette(palette);

                setResultMessage({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Color palette generated successfully'
                });
            } catch (error) {
                console.error('Error generating palette:', error);
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to generate color palette'
                });
            } finally {
                setIsProcessing(false);
            }
        }, 1000);
    };

    // Reset all image generation fields
    const resetImageGenerator = () => {
        setImagePrompt('');
        setGeneratedImage(null);
        setImageEditedVersion(null);
        setImageEditMode('generate');
        setImageStyle('realistic');
        setImageEnhanceLevel(50);
        setImageBackgroundPrompt('');
    };

    // Get the image source to edit (selected image or generated image)
    const getImageToEdit = () => {
        if (selectedImage) {
            return selectedImage.value;
        }
        return generatedImage;
    };

    // Update the selected image in the canvas
    const updateSelectedImage = (newStyle) => {
        if (selectedImage) {
            // Update the existing image in the canvas
            // The keepSelection parameter in updateElement will handle maintaining the selection
            updateElement(selectedImage.id, {
                style: {
                    ...selectedImage.style,
                    ...newStyle
                }
            }, true); // true = keep selection

            return true;
        }
        return false;
    };

    // Handle image style change
    const handleApplyImageStyle = () => {
        const imageToEdit = getImageToEdit();
        if (!imageToEdit) return;

        setIsProcessing(true);
        setResultMessage({
            severity: 'info',
            summary: 'Processing',
            detail: 'Applying style...'
        });

        // Simulate processing delay
        setTimeout(() => {
            // If we're editing a selected image, update it directly
            if (selectedImage) {
                // Apply the style filter to the selected image
                const newStyle = {
                    filter: imageStyle !== 'realistic' ? imageStyles[imageStyle].filter : 'none'
                };

                updateSelectedImage(newStyle);
            } else {
                // Otherwise, just update the preview
                setImageEditedVersion(imageToEdit);
            }

            setIsProcessing(false);
            setResultMessage({
                severity: 'success',
                summary: 'Success',
                detail: 'Style applied successfully'
            });
        }, 1000);
    };

    // Handle image enhancement
    const handleEnhanceImage = () => {
        const imageToEdit = getImageToEdit();
        if (!imageToEdit) return;

        setIsProcessing(true);
        setResultMessage({
            severity: 'info',
            summary: 'Processing',
            detail: 'Enhancing image...'
        });

        // Simulate processing delay
        setTimeout(() => {
            // If we're editing a selected image, update it directly
            if (selectedImage) {
                // Apply enhancement filter based on level
                const enhanceFilter = `contrast(${1 + imageEnhanceLevel/100}) brightness(${1 + imageEnhanceLevel/200}) saturate(${1 + imageEnhanceLevel/100})`;

                updateSelectedImage({ filter: enhanceFilter });
            } else {
                // Otherwise, just update the preview
                setImageEditedVersion(imageToEdit);
            }

            setIsProcessing(false);
            setResultMessage({
                severity: 'success',
                summary: 'Success',
                detail: 'Image enhanced successfully'
            });
        }, 1500);
    };

    // Function to remove background using Canvas API
    const removeBackground = (imageUrl) => {
        return new Promise((resolve, reject) => {
            try {
                // Create a new image object
                const img = new Image();
                img.crossOrigin = "Anonymous";

                // Handle image load event
                img.onload = () => {
                    try {
                        // Create canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set canvas dimensions to match image
                        canvas.width = img.width;
                        canvas.height = img.height;

                        // Draw image on canvas
                        ctx.drawImage(img, 0, 0);

                        try {
                            // Get image data
                            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                            const data = imageData.data;

                            // Simple background removal algorithm
                            // This will make white and light colors transparent
                            for (let i = 0; i < data.length; i += 4) {
                                const r = data[i];
                                const g = data[i + 1];
                                const b = data[i + 2];

                                // Check if pixel is white or very light (background)
                                const isLight = r > 240 && g > 240 && b > 240;

                                // Make light pixels transparent
                                if (isLight) {
                                    data[i + 3] = 0; // Alpha channel
                                }
                            }

                            // Put modified image data back on canvas
                            ctx.putImageData(imageData, 0, 0);

                            // Convert canvas to data URL
                            const dataUrl = canvas.toDataURL('image/png');
                            resolve(dataUrl);
                        } catch (error) {
                            console.error('Error processing image data:', error);
                            // Fallback: return original image if processing fails
                            resolve(imageUrl);
                        }
                    } catch (error) {
                        console.error('Error creating canvas:', error);
                        // Fallback: return original image if canvas creation fails
                        resolve(imageUrl);
                    }
                };

                // Handle image load error
                img.onerror = (err) => {
                    console.error('Error loading image:', err);
                    // Fallback: return original image if loading fails
                    resolve(imageUrl);
                };

                // Set image source to start loading
                img.src = imageUrl;

                // Handle case where image might be cached and already loaded
                if (img.complete) {
                    img.onload();
                }
            } catch (error) {
                console.error('Error in removeBackground:', error);
                // Fallback: return original image if any error occurs
                resolve(imageUrl);
            }
        });
    };

    // Handle background removal/replacement
    const handleChangeBackground = () => {
        const imageToEdit = getImageToEdit();
        if (!imageToEdit) return;

        setIsProcessing(true);
        setResultMessage({
            severity: 'info',
            summary: 'Processing',
            detail: 'Changing background...'
        });

        if (imageBackgroundPrompt.includes('transparent')) {
            // For background removal, use Canvas API
            if (selectedImage) {
                // Process the selected image
                removeBackground(selectedImage.value)
                    .then(transparentImageUrl => {
                        // Update the existing image with the transparent version
                        updateElement(selectedImage.id, {
                            value: transparentImageUrl,
                            style: {
                                ...selectedImage.style,
                                filter: 'drop-shadow(0px 0px 5px rgba(0,0,0,0.2))',
                                backgroundRemoved: true
                            }
                        });

                        // Keep the same element selected
                        setSelectedIds([selectedImage.id]);

                        setIsProcessing(false);
                        setResultMessage({
                            severity: 'success',
                            summary: 'Success',
                            detail: 'Background removed successfully'
                        });
                    })
                    .catch(err => {
                        console.error('Error removing background:', err);
                        setIsProcessing(false);
                        setResultMessage({
                            severity: 'error',
                            summary: 'Error',
                            detail: 'Failed to remove background'
                        });
                    });
            } else if (generatedImage) {
                // Process the generated image
                removeBackground(generatedImage)
                    .then(transparentImageUrl => {
                        setImageEditedVersion(transparentImageUrl);
                        setIsProcessing(false);
                        setResultMessage({
                            severity: 'success',
                            summary: 'Success',
                            detail: 'Background removed successfully'
                        });
                    })
                    .catch(err => {
                        console.error('Error removing background:', err);
                        setIsProcessing(false);
                        setResultMessage({
                            severity: 'error',
                            summary: 'Error',
                            detail: 'Failed to remove background'
                        });
                    });
            }
        } else {
            // For other backgrounds, just update the style
            setTimeout(() => {
                if (selectedImage) {
                    updateSelectedImage({
                        backgroundPrompt: imageBackgroundPrompt,
                        style: {
                            ...selectedImage.style,
                            boxShadow: '0 0 15px rgba(0,0,0,0.2)',
                            outline: 'none',
                            filter: 'contrast(1.05) brightness(1.05)'
                        }
                    });
                } else {
                    setImageEditedVersion(imageToEdit);
                }

                setIsProcessing(false);
                setResultMessage({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Background changed successfully'
                });
            }, 1000);
        }
    };

    // Add image to canvas
    const handleAddImageToCanvas = (imageUrl) => {
        try {
            // Add the image element directly using the context function
            const newElementId = addElement('img', imageUrl, {
                width: 300,
                height: 200
            });

            // Select the new element
            if (newElementId) {
                setSelectedIds([newElementId]);
            }

            setResultMessage({
                severity: 'success',
                summary: 'Added',
                detail: 'Image added to canvas'
            });
        } catch (error) {
            console.error('Error adding image to canvas:', error);
            setResultMessage({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to add image to canvas'
            });
        }
    };

    // Reset all text styling fields
    const resetTextStyling = () => {
        setTextContent('Welcome to our design platform');
        setFontSize(16);
        setFontFamily('Arial, sans-serif');
        setFontWeight('normal');
        setTextColor('#000000');
        setBackgroundColor('transparent');
        setTextAlign('left');
        setLineHeight(1.5);
        setLetterSpacing(0);
        setTextDecoration('none');
        setTextTransform('none');
        setTextEffect('none');
        setTextStyle(null);
        setTextOpacity(100);
        setTextRotation(0);
        setTextShadowColor('#000000');
        setTextShadowBlur(2);
        setTextShadowOffset(2);
    };

    // Apply a text style preset
    const applyTextStylePreset = (preset) => {
        switch(preset) {
            case 'heading1':
                setFontSize(32);
                setFontWeight('bold');
                setFontFamily('Arial, sans-serif');
                setTextColor('#333333');
                setTextAlign('left');
                setLineHeight(1.2);
                setLetterSpacing(0);
                setTextTransform('none');
                setTextEffect('none');
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case 'heading2':
                setFontSize(24);
                setFontWeight('bold');
                setFontFamily('Arial, sans-serif');
                setTextColor('#444444');
                setTextAlign('left');
                setLineHeight(1.3);
                setLetterSpacing(0);
                setTextTransform('none');
                setTextEffect('none');
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case 'subtitle':
                setFontSize(18);
                setFontWeight('600');
                setFontFamily('Arial, sans-serif');
                setTextColor('#666666');
                setTextAlign('left');
                setLineHeight(1.4);
                setLetterSpacing(0.5);
                setTextTransform('none');
                setTextEffect('none');
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case 'paragraph':
                setFontSize(16);
                setFontWeight('normal');
                setFontFamily('Arial, sans-serif');
                setTextColor('#333333');
                setTextAlign('left');
                setLineHeight(1.6);
                setLetterSpacing(0);
                setTextTransform('none');
                setTextEffect('none');
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case 'quote':
                setFontSize(18);
                setFontWeight('normal');
                setFontFamily('Georgia, serif');
                setTextColor('#555555');
                setTextAlign('center');
                setLineHeight(1.6);
                setLetterSpacing(0.5);
                setTextTransform('none');
                setTextEffect('none');
                setTextStyle('italic');
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case 'callout':
                setFontSize(20);
                setFontWeight('bold');
                setFontFamily('Arial, sans-serif');
                setTextColor('#ffffff');
                setBackgroundColor('#4338ca');
                setTextAlign('center');
                setLineHeight(1.4);
                setLetterSpacing(0.5);
                setTextTransform('uppercase');
                setTextEffect('shadow');
                setTextShadowColor('#000000');
                setTextShadowBlur(2);
                setTextShadowOffset(1);
                setTextRotation(0);
                break;
            case 'elegant':
                setFontSize(22);
                setFontWeight('normal');
                setFontFamily('Times New Roman, serif');
                setTextColor('#333333');
                setTextAlign('center');
                setLineHeight(1.6);
                setLetterSpacing(1);
                setTextTransform('none');
                setTextEffect('none');
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case 'modern':
                setFontSize(18);
                setFontWeight('300');
                setFontFamily('Helvetica, sans-serif');
                setTextColor('#222222');
                setTextAlign('left');
                setLineHeight(1.8);
                setLetterSpacing(0.8);
                setTextTransform('none');
                setTextEffect('none');
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case '3d':
                setFontSize(32);
                setFontWeight('bold');
                setFontFamily('Impact, sans-serif');
                setTextColor('#2563eb');
                setTextAlign('center');
                setLineHeight(1.2);
                setLetterSpacing(1);
                setTextTransform('uppercase');
                setTextEffect('3d');
                setBackgroundColor('transparent');
                setTextShadowColor('#000000');
                setTextShadowBlur(1);
                setTextShadowOffset(1);
                setTextRotation(0);
                break;
            case 'dramatic':
                setFontSize(28);
                setFontWeight('800');
                setFontFamily('Impact, sans-serif');
                setTextColor('#111111');
                setTextAlign('center');
                setLineHeight(1.2);
                setLetterSpacing(1.5);
                setTextTransform('uppercase');
                setTextEffect('shadow');
                setTextShadowColor('#777777');
                setTextShadowBlur(4);
                setTextShadowOffset(3);
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            case 'playful':
                setFontSize(20);
                setFontWeight('bold');
                setFontFamily('Comic Sans MS, cursive');
                setTextColor('#ff6600');
                setTextAlign('center');
                setLineHeight(1.4);
                setLetterSpacing(0);
                setTextTransform('none');
                setTextEffect('shadow');
                setTextShadowColor('#ffcc00');
                setTextShadowBlur(2);
                setTextShadowOffset(1);
                setBackgroundColor('transparent');
                setTextRotation(0);
                break;
            default:
                break;
        }
    };

    // Reset all color palette generation fields
    const resetPaletteGenerator = () => {
        setBaseColor('#6366F1');
        setPaletteType('complementary');
        setGeneratedPalette(null);
    };

    return (
        <div className="ai-tools-hub h-full" style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', backgroundColor: 'white' }} onClick={(e) => e.stopPropagation()}>
            <div className="p-4 border-b border-gray-200 bg-white z-10" style={{ flexShrink: 0 }}>
                <h3 className="text-lg font-medium flex items-center">
                    <TbBrandOpenai className="mr-2 text-purple-600" />
                    AI Design Assistant
                </h3>

                {resultMessage && (
                    <div className="mt-3">
                        <Message
                            severity={resultMessage.severity}
                            text={resultMessage.detail}
                            style={{ width: '100%' }}
                        />
                    </div>
                )}

                {isProcessing && (
                    <div className="mt-3">
                        <ProgressBar mode="indeterminate" style={{ height: '4px' }} />
                    </div>
                )}
            </div>

            <div style={{
                flexGrow: 1,
                overflowY: 'auto',
                padding: '0',
                height: '100%',
                position: 'relative'
            }}>
                <TabView activeIndex={activeTab} onTabChange={(e) => setActiveTab(e.index)} className="ai-tools-tabs">
                    <TabPanel header="Image" leftIcon={<FaImage className="mr-2" />}>
                        <div className="p-4">
                            <h4 className="text-md font-medium mb-3 flex items-center">
                                <FaImage className="mr-2 text-green-600" />
                                AI Image Tools
                            </h4>

                            {/* Selected Image Info */}
                            {selectedImage ? (
                                <div className="mb-4 p-3 bg-blue-50 rounded border border-blue-100">
                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded mr-2">صورة محددة</span>
                                        Selected Image:
                                    </h5>
                                    <div className="flex items-center">
                                        <div className="w-16 h-16 mr-3 border rounded overflow-hidden">
                                            <img
                                                src={selectedImage.value}
                                                alt="Selected"
                                                className="w-full h-full object-cover"
                                            />
                                        </div>
                                        <div>
                                            <p className="text-xs text-blue-800 font-medium">ID: {selectedImage.id}</p>
                                            <p className="text-xs text-blue-600">Apply AI tools to edit this image directly on canvas</p>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="mb-4 p-3 bg-yellow-50 rounded border border-yellow-100">
                                    <p className="text-sm text-yellow-700">
                                        <span className="font-medium">Tip:</span> Select an image on the canvas to edit it directly, or generate a new one below.
                                    </p>
                                </div>
                            )}

                            {/* Image Tool Tabs */}
                            <div className="mb-4">
                                <div className="flex flex-wrap border-b border-gray-200">
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'generate' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('generate')}
                                    >
                                        <RiAiGenerate className="inline mr-1" /> Generate
                                    </div>
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'enhance' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('enhance')}
                                    >
                                        <MdOutlineAutoFixHigh className="inline mr-1" /> Enhance
                                    </div>
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'style' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('style')}
                                    >
                                        <MdOutlinePhotoFilter className="inline mr-1" /> Style
                                    </div>
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'background' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('background')}
                                    >
                                        <BiPaint className="inline mr-1" /> Background
                                    </div>
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'crop' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('crop')}
                                    >
                                        <MdPhotoSizeSelectLarge className="inline mr-1" /> Crop
                                    </div>
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'filters' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('filters')}
                                    >
                                        <MdFilterVintage className="inline mr-1" /> Filters
                                    </div>
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'adjust' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('adjust')}
                                    >
                                        <MdSettings className="inline mr-1" /> Adjust
                                    </div>
                                    <div
                                        className={`px-3 py-2 cursor-pointer ${imageEditMode === 'effects' ? 'border-b-2 border-green-500 text-green-600 font-medium' : 'text-gray-500'}`}
                                        onClick={() => setImageEditMode('effects')}
                                    >
                                        <MdOutlineAutoAwesome className="inline mr-1" /> Effects
                                    </div>
                                </div>
                            </div>

                            {/* Generate Image Tab */}
                            {imageEditMode === 'generate' && (
                                <div>
                                    <div className="mb-3">
                                        <label htmlFor="imagePrompt" className="block text-sm font-medium mb-1">Describe the image you want</label>
                                        <InputText
                                            id="imagePrompt"
                                            value={imagePrompt}
                                            onChange={(e) => setImagePrompt(e.target.value)}
                                            placeholder="e.g., A mountain landscape"
                                            className="w-full"
                                            disabled={isProcessing}
                                        />

                                        <div className="mt-2">
                                            <div className="flex flex-wrap gap-1">
                                                <Chip label="Landscape" className="text-xs cursor-pointer" onClick={() => setImagePrompt('A landscape')} />
                                                <Chip label="Portrait" className="text-xs cursor-pointer" onClick={() => setImagePrompt('A portrait')} />
                                                <Chip label="Nature" className="text-xs cursor-pointer" onClick={() => setImagePrompt('Nature scene')} />
                                                <Chip label="City" className="text-xs cursor-pointer" onClick={() => setImagePrompt('City skyline')} />
                                                <Chip label="Beach" className="text-xs cursor-pointer" onClick={() => setImagePrompt('Beach sunset')} />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex justify-between mb-4">
                                        <Button
                                            label="Reset"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                            onClick={resetImageGenerator}
                                            disabled={isProcessing}
                                        />
                                        <Button
                                            label={isProcessing ? "Generating..." : "Generate Image"}
                                            icon="pi pi-image"
                                            loading={isProcessing}
                                            onClick={handleGenerateImage}
                                            disabled={!imagePrompt || isProcessing}
                                            className="p-button-sm"
                                        />
                                    </div>
                                </div>
                            )}

                            {/* Enhance Image Tab */}
                            {imageEditMode === 'enhance' && (
                                <div>
                                    <div className="mb-3">
                                        <label className="block text-sm font-medium mb-1">Enhancement Level: {imageEnhanceLevel}%</label>
                                        <Slider
                                            value={imageEnhanceLevel}
                                            onChange={(e) => setImageEnhanceLevel(e.value)}
                                            min={0}
                                            max={100}
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                    </div>

                                    <div className="flex justify-between mb-4">
                                        <Button
                                            label="Reset"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                            onClick={() => setImageEnhanceLevel(50)}
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                        <Button
                                            label={isProcessing ? "Enhancing..." : "Enhance Image"}
                                            icon="pi pi-image"
                                            loading={isProcessing}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleEnhanceImage();
                                            }}
                                            disabled={(!generatedImage && !selectedImage) || isProcessing}
                                            className="p-button-sm"
                                        />
                                    </div>
                                </div>
                            )}

                            {/* Style Image Tab */}
                            {imageEditMode === 'style' && (
                                <div>
                                    <div className="mb-3">
                                        <label className="block text-sm font-medium mb-2">Select Style</label>
                                        <div className="grid grid-cols-2 gap-2">
                                            {Object.entries(imageStyles).map(([key, style]) => (
                                                <div
                                                    key={key}
                                                    className={`p-2 border rounded cursor-pointer text-center ${imageStyle === key ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}
                                                    onClick={() => setImageStyle(key)}
                                                >
                                                    {style.label}
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    <div className="flex justify-between mb-4">
                                        <Button
                                            label="Reset"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                            onClick={() => setImageStyle('realistic')}
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                        <Button
                                            label={isProcessing ? "Applying..." : "Apply Style"}
                                            icon="pi pi-image"
                                            loading={isProcessing}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleApplyImageStyle();
                                            }}
                                            disabled={(!generatedImage && !selectedImage) || isProcessing}
                                            className="p-button-sm"
                                        />
                                    </div>
                                </div>
                            )}

                            {/* Background Image Tab */}
                            {imageEditMode === 'background' && (
                                <div>
                                    <div className="mb-3">
                                        <label htmlFor="backgroundPrompt" className="block text-sm font-medium mb-1">Describe the new background</label>
                                        <InputText
                                            id="backgroundPrompt"
                                            value={imageBackgroundPrompt}
                                            onChange={(e) => setImageBackgroundPrompt(e.target.value)}
                                            placeholder="e.g., A beach sunset"
                                            className="w-full"
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />

                                        <div className="mt-2">
                                            <div className="flex flex-wrap gap-1">
                                                <Chip label="Beach" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('beach background')} />
                                                <Chip label="Space" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('space background')} />
                                                <Chip label="Forest" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('forest background')} />
                                                <Chip label="City" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('city background')} />
                                                <Chip label="Mountains" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('mountain background')} />
                                                <Chip label="Abstract" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('abstract colorful background')} />
                                                <Chip label="Gradient" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('gradient background')} />
                                                <Chip label="Office" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('modern office background')} />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex justify-between mb-4">
                                        <Button
                                            label="Reset"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                            onClick={() => setImageBackgroundPrompt('')}
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                        <Button
                                            label={isProcessing ? "Changing..." : "Change Background"}
                                            icon="pi pi-image"
                                            loading={isProcessing}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleChangeBackground();
                                            }}
                                            disabled={((!generatedImage && !selectedImage) || !imageBackgroundPrompt || isProcessing)}
                                            className="p-button-sm"
                                        />
                                    </div>
                                </div>
                            )}

                            {/* Image Preview - Only show if no image is selected or we're in generate mode */}
                            {(!selectedImage || imageEditMode === 'generate') && (
                                <div className="mt-4 p-3 border rounded bg-gradient-to-r from-green-50 to-teal-50 shadow-sm">
                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                        <span className="bg-green-500 text-white text-xs px-2 py-1 rounded mr-2">الصورة</span>
                                        Image Preview:
                                    </h5>
                                    {generatedImage ? (
                                        <>
                                            <div className="border rounded overflow-hidden bg-white shadow-sm">
                                                <img
                                                    src={imageEditedVersion || generatedImage}
                                                    alt="Generated"
                                                    className="max-w-full h-auto mx-auto"
                                                    style={{
                                                        maxHeight: '200px',
                                                        objectFit: 'contain',
                                                        filter: imageEditMode === 'style' && imageStyle !== 'realistic' ? imageStyles[imageStyle].filter : 'none',
                                                        backgroundColor: imageEditMode === 'background' && imageBackgroundPrompt.includes('transparent') ? 'transparent' : undefined
                                                    }}
                                                />
                                            </div>
                                            <div className="mt-3 flex justify-end">
                                                <Button
                                                    label="Add to Canvas"
                                                    icon="pi pi-plus"
                                                    className="p-button-sm p-button-outlined"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleAddImageToCanvas(imageEditedVersion || generatedImage);
                                                    }}
                                                />
                                            </div>
                                        </>
                                    ) : (
                                        <p className="text-sm text-gray-500 italic p-2 bg-white rounded border border-gray-100">
                                            الصورة ستظهر هنا بعد إنشائها
                                        </p>
                                    )}
                                </div>
                            )}

                            {/* Preview of edits on selected image - Only show if an image is selected and we're not in generate mode */}
                            {selectedImage && imageEditMode !== 'generate' && (
                                <div className="mt-4 p-3 border rounded bg-gradient-to-r from-blue-50 to-indigo-50 shadow-sm">
                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded mr-2">معاينة التعديلات</span>
                                        Edit Preview:
                                    </h5>
                                    <div className="flex flex-col md:flex-row gap-4">
                                        <div className="flex-1 border rounded overflow-hidden bg-white shadow-sm p-2">
                                            <h6 className="text-xs font-medium text-center mb-2 text-gray-500">Original</h6>
                                            <img
                                                src={selectedImage.value}
                                                alt="Original"
                                                className="max-w-full h-auto mx-auto"
                                                style={{
                                                    maxHeight: '150px',
                                                    objectFit: 'contain'
                                                }}
                                            />
                                        </div>
                                        <div className="flex-1 border rounded overflow-hidden bg-white shadow-sm p-2">
                                            <h6 className="text-xs font-medium text-center mb-2 text-gray-500">With Edits</h6>
                                            {imageEditMode === 'background' && imageBackgroundPrompt.includes('transparent') ? (
                                                <div className="relative">
                                                    <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
                                                        {[...Array(64)].map((_, i) => (
                                                            <div
                                                                key={i}
                                                                className={`${(Math.floor(i / 8) + i % 8) % 2 === 0 ? 'bg-gray-200' : 'bg-white'}`}
                                                            ></div>
                                                        ))}
                                                    </div>
                                                    <img
                                                        src={selectedImage.value}
                                                        alt="With Edits"
                                                        className="max-w-full h-auto mx-auto relative"
                                                        style={{
                                                            maxHeight: '150px',
                                                            objectFit: 'contain',
                                                            filter: 'contrast(1.2) brightness(1.1)',
                                                            mixBlendMode: 'multiply'
                                                        }}
                                                    />
                                                </div>
                                            ) : (
                                                <img
                                                    src={selectedImage.value}
                                                    alt="With Edits"
                                                    className="max-w-full h-auto mx-auto"
                                                    style={{
                                                        maxHeight: '150px',
                                                        objectFit: 'contain',
                                                        filter: imageEditMode === 'style' && imageStyle !== 'realistic'
                                                            ? imageStyles[imageStyle].filter
                                                            : imageEditMode === 'enhance'
                                                                ? `contrast(${1 + imageEnhanceLevel/100}) brightness(${1 + imageEnhanceLevel/200}) saturate(${1 + imageEnhanceLevel/100})`
                                                                : 'none'
                                                    }}
                                                />
                                            )}
                                        </div>
                                    </div>
                                    <div className="mt-3 text-xs text-center text-gray-500">
                                        Changes are applied directly to the selected image on the canvas
                                    </div>
                                </div>
                            )}
                        </div>
                    </TabPanel>

                    <TabPanel header="Text" leftIcon={<FaFont className="mr-2" />}>
                        <div className="p-4">
                            <h4 className="text-md font-medium mb-3 flex items-center">
                                <BiText className="mr-2 text-blue-600" />
                                Text Styling Tool
                            </h4>

                            {/* Instructions panel */}
                            <div className="mb-4 p-3 bg-blue-50 rounded border border-blue-100">
                                <h5 className="text-sm font-medium mb-2 flex items-center">
                                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded mr-2">كيفية الاستخدام</span>
                                    How to use:
                                </h5>
                                <ol className="text-xs text-gray-700 list-decimal pl-5 space-y-1">
                                    <li>اكتب النص الذي تريد تنسيقه في مربع النص</li>
                                    <li>اختر نمط النص من القوالب الجاهزة أو قم بتخصيص التنسيق</li>
                                    <li>عدل خصائص النص (الحجم، اللون، الخط، إلخ)</li>
                                    <li>شاهد النتيجة مباشرة في المعاينة</li>
                                    <li>انقر على "Add to Canvas" لإضافة النص إلى التصميم</li>
                                </ol>
                            </div>

                            {/* Text Input */}
                            <div className="mb-4">
                                <label htmlFor="textContent" className="block text-sm font-medium mb-1">
                                    <span className="bg-yellow-100 px-2 py-0.5 rounded text-yellow-800 mr-1">1</span>
                                    Your Text
                                </label>
                                <InputText
                                    id="textContent"
                                    value={textContent}
                                    onChange={(e) => setTextContent(e.target.value)}
                                    placeholder="Enter your text here"
                                    className="w-full"
                                />
                            </div>

                            {/* Preview - Moved to top */}
                            <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">
                                    <span className="bg-yellow-100 px-2 py-0.5 rounded text-yellow-800 mr-1">2</span>
                                    Preview
                                </label>
                                <div
                                    className="p-4 border-2 border-blue-200 rounded bg-white shadow-sm overflow-hidden"
                                    style={{
                                        minHeight: '100px',
                                        transform: `rotate(${textRotation}deg)`
                                    }}
                                >
                                    <div
                                        style={{
                                            fontFamily: fontFamily,
                                            fontSize: `${fontSize}px`,
                                            fontWeight: fontWeight,
                                            color: textColor,
                                            backgroundColor: backgroundColor,
                                            textAlign: textAlign,
                                            lineHeight: lineHeight,
                                            letterSpacing: `${letterSpacing}px`,
                                            textDecoration: textDecoration,
                                            textTransform: textTransform,
                                            fontStyle: textStyle,
                                            opacity: textOpacity / 100,
                                            textShadow: textEffect === 'shadow' ? `${textShadowOffset}px ${textShadowOffset}px ${textShadowBlur}px ${textShadowColor}` : 'none',
                                            WebkitTextStroke: textEffect === 'outline' ? '1px #000000' : 'none',
                                            padding: backgroundColor !== 'transparent' ? '10px' : '0',
                                            transform: textEffect === '3d' ? 'perspective(500px) rotateX(10deg) rotateY(5deg)' : 'none',
                                        }}
                                    >
                                        {textContent || 'Welcome to our design platform'}
                                    </div>
                                </div>

                                {/* Add to Canvas Button - Moved next to preview */}
                                <div className="flex justify-end mt-2">
                                    <Button
                                        label="Add to Canvas"
                                        icon="pi pi-plus"
                                        className="p-button-sm p-button-raised p-button-primary"
                                        style={{
                                            backgroundColor: '#4338ca',
                                            borderColor: '#4338ca',
                                            fontWeight: 'bold',
                                            boxShadow: '0 4px 6px -1px rgba(67, 56, 202, 0.3)'
                                        }}
                                        onClick={() => {
                                            // Create style object for the text
                                            const textStyle2 = {
                                                fontFamily: fontFamily,
                                                fontSize: `${fontSize}px`,
                                                fontWeight: fontWeight,
                                                color: textColor,
                                                backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
                                                textAlign: textAlign,
                                                lineHeight: lineHeight,
                                                letterSpacing: `${letterSpacing}px`,
                                                textDecoration: textDecoration,
                                                textTransform: textTransform,
                                                fontStyle: textStyle,
                                                opacity: textOpacity / 100,
                                                textShadow: textEffect === 'shadow' ? `${textShadowOffset}px ${textShadowOffset}px ${textShadowBlur}px ${textShadowColor}` : undefined,
                                                WebkitTextStroke: textEffect === 'outline' ? '1px #000000' : undefined,
                                                transform: textEffect === '3d' ? 'perspective(500px) rotateX(10deg) rotateY(5deg)' : textRotation !== 0 ? `rotate(${textRotation}deg)` : undefined,
                                            };

                                            try {
                                                // Add the element directly using the context function
                                                const newElementId = addElement('text', textContent || 'Welcome to our design platform', {
                                                    style: textStyle2,
                                                    width: 300,
                                                    height: 100,
                                                    fontFamily: fontFamily,
                                                    fontSize: fontSize,
                                                    fontWeight: fontWeight,
                                                    color: textColor,
                                                    backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
                                                    textAlign: textAlign,
                                                    lineHeight: lineHeight,
                                                    letterSpacing: letterSpacing,
                                                    textDecoration: textDecoration,
                                                    textTransform: textTransform,
                                                    fontStyle: textStyle,
                                                    opacity: textOpacity / 100,
                                                    textShadow: textEffect === 'shadow' ? `${textShadowOffset}px ${textShadowOffset}px ${textShadowBlur}px ${textShadowColor}` : undefined,
                                                    WebkitTextStroke: textEffect === 'outline' ? '1px #000000' : undefined,
                                                    transform: textEffect === '3d' ? 'perspective(500px) rotateX(10deg) rotateY(5deg)' : textRotation !== 0 ? `rotate(${textRotation}deg)` : undefined
                                                });

                                                // Select the new element
                                                if (newElementId) {
                                                    setSelectedIds([newElementId]);
                                                }

                                                setResultMessage({
                                                    severity: 'success',
                                                    summary: 'Added',
                                                    detail: 'Text added to canvas'
                                                });
                                            } catch (error) {
                                                console.error('Error adding text to canvas:', error);
                                                setResultMessage({
                                                    severity: 'error',
                                                    summary: 'Error',
                                                    detail: 'Failed to add text to canvas'
                                                });
                                            }
                                        }}
                                    />
                                </div>
                            </div>

                            {/* Style Presets */}
                            <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">
                                    <span className="bg-yellow-100 px-2 py-0.5 rounded text-yellow-800 mr-1">3</span>
                                    Style Presets
                                </label>
                                <div className="grid grid-cols-2 gap-2">
                                    <Button label="Heading 1" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('heading1')} />
                                    <Button label="Heading 2" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('heading2')} />
                                    <Button label="Subtitle" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('subtitle')} />
                                    <Button label="Paragraph" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('paragraph')} />
                                    <Button label="Quote" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('quote')} />
                                    <Button label="Callout" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('callout')} />
                                    <Button label="Elegant" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('elegant')} />
                                    <Button label="Modern" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('modern')} />
                                    <Button label="3D Text" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('3d')} />
                                    <Button label="Playful" className="p-button-sm p-button-outlined" onClick={() => applyTextStylePreset('playful')} />
                                </div>
                            </div>

                            {/* Custom Styling */}
                            <div className="mb-4">
                                <div className="flex justify-between items-center mb-2">
                                    <label className="block text-sm font-medium">
                                        <span className="bg-yellow-100 px-2 py-0.5 rounded text-yellow-800 mr-1">3</span>
                                        Custom Styling
                                    </label>
                                    <Button
                                        label="Reset"
                                        icon="pi pi-refresh"
                                        className="p-button-outlined p-button-sm"
                                        onClick={resetTextStyling}
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-3">
                                    {/* Font Family */}
                                    <div className="col-span-2">
                                        <label className="block text-xs font-medium mb-1">Font Family</label>
                                        <Dropdown
                                            value={fontFamily}
                                            options={fontFamilyOptions}
                                            onChange={(e) => setFontFamily(e.value)}
                                            className="w-full"
                                        />
                                    </div>

                                    {/* Font Size */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Font Size: {fontSize}px</label>
                                        <Slider
                                            value={fontSize}
                                            onChange={(e) => setFontSize(e.value)}
                                            min={8}
                                            max={72}
                                        />
                                    </div>

                                    {/* Font Weight */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Font Weight</label>
                                        <Dropdown
                                            value={fontWeight}
                                            options={fontWeightOptions}
                                            onChange={(e) => setFontWeight(e.value)}
                                            className="w-full"
                                        />
                                    </div>

                                    {/* Text Color */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Text Color</label>
                                        <div className="flex items-center">
                                            <input
                                                type="color"
                                                value={textColor}
                                                onChange={(e) => setTextColor(e.target.value)}
                                                className="w-8 h-8 mr-2 border rounded cursor-pointer"
                                            />
                                            <InputText
                                                value={textColor}
                                                onChange={(e) => setTextColor(e.target.value)}
                                                className="w-full"
                                            />
                                        </div>
                                    </div>

                                    {/* Background Color */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Background Color</label>
                                        <div className="flex items-center">
                                            <input
                                                type="color"
                                                value={backgroundColor === 'transparent' ? '#ffffff' : backgroundColor}
                                                onChange={(e) => setBackgroundColor(e.target.value)}
                                                className="w-8 h-8 mr-2 border rounded cursor-pointer"
                                            />
                                            <InputText
                                                value={backgroundColor}
                                                onChange={(e) => setBackgroundColor(e.target.value)}
                                                className="w-full"
                                            />
                                        </div>
                                    </div>

                                    {/* Text Alignment */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Text Alignment</label>
                                        <Dropdown
                                            value={textAlign}
                                            options={textAlignOptions}
                                            onChange={(e) => setTextAlign(e.value)}
                                            className="w-full"
                                        />
                                    </div>

                                    {/* Line Height */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Line Height: {lineHeight}</label>
                                        <Slider
                                            value={lineHeight}
                                            onChange={(e) => setLineHeight(e.value)}
                                            min={0.8}
                                            max={3}
                                            step={0.1}
                                        />
                                    </div>

                                    {/* Letter Spacing */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Letter Spacing: {letterSpacing}px</label>
                                        <Slider
                                            value={letterSpacing}
                                            onChange={(e) => setLetterSpacing(e.value)}
                                            min={-2}
                                            max={10}
                                            step={0.1}
                                        />
                                    </div>

                                    {/* Text Decoration */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Text Decoration</label>
                                        <Dropdown
                                            value={textDecoration}
                                            options={textDecorationOptions}
                                            onChange={(e) => setTextDecoration(e.value)}
                                            className="w-full"
                                        />
                                    </div>

                                    {/* Text Transform */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Text Transform</label>
                                        <Dropdown
                                            value={textTransform}
                                            options={textTransformOptions}
                                            onChange={(e) => setTextTransform(e.value)}
                                            className="w-full"
                                        />
                                    </div>

                                    {/* Text Effect */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Text Effect</label>
                                        <Dropdown
                                            value={textEffect}
                                            options={textEffectOptions}
                                            onChange={(e) => setTextEffect(e.value)}
                                            className="w-full"
                                        />
                                    </div>

                                    {/* Opacity */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Opacity: {textOpacity}%</label>
                                        <Slider
                                            value={textOpacity}
                                            onChange={(e) => setTextOpacity(e.value)}
                                            min={0}
                                            max={100}
                                        />
                                    </div>

                                    {/* Rotation */}
                                    <div>
                                        <label className="block text-xs font-medium mb-1">Rotation: {textRotation}°</label>
                                        <Slider
                                            value={textRotation}
                                            onChange={(e) => setTextRotation(e.value)}
                                            min={-180}
                                            max={180}
                                        />
                                    </div>

                                    {/* Shadow Controls - only show if effect is shadow */}
                                    {textEffect === 'shadow' && (
                                        <>
                                            <div>
                                                <label className="block text-xs font-medium mb-1">Shadow Color</label>
                                                <div className="flex items-center">
                                                    <input
                                                        type="color"
                                                        value={textShadowColor}
                                                        onChange={(e) => setTextShadowColor(e.target.value)}
                                                        className="w-8 h-8 mr-2 border rounded cursor-pointer"
                                                    />
                                                    <InputText
                                                        value={textShadowColor}
                                                        onChange={(e) => setTextShadowColor(e.target.value)}
                                                        className="w-full"
                                                    />
                                                </div>
                                            </div>

                                            <div>
                                                <label className="block text-xs font-medium mb-1">Shadow Blur: {textShadowBlur}px</label>
                                                <Slider
                                                    value={textShadowBlur}
                                                    onChange={(e) => setTextShadowBlur(e.value)}
                                                    min={0}
                                                    max={20}
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-xs font-medium mb-1">Shadow Offset: {textShadowOffset}px</label>
                                                <Slider
                                                    value={textShadowOffset}
                                                    onChange={(e) => setTextShadowOffset(e.value)}
                                                    min={0}
                                                    max={10}
                                                />
                                            </div>
                                        </>
                                    )}
                                </div>
                            </div>


                        </div>
                    </TabPanel>

                    <TabPanel header="Colors" leftIcon={<FaPalette className="mr-2" />}>
                        <div className="p-4">
                            <h4 className="text-md font-medium mb-3 flex items-center">
                                <IoColorPaletteOutline className="mr-2 text-pink-600" />
                                AI Color Palette Generator
                            </h4>

                            {/* Instructions panel */}
                            <div className="mb-4 p-3 bg-pink-50 rounded border border-pink-100">
                                <h5 className="text-sm font-medium mb-2 flex items-center">
                                    <span className="bg-pink-500 text-white text-xs px-2 py-1 rounded mr-2">كيفية الاستخدام</span>
                                    How to use:
                                </h5>
                                <ol className="text-xs text-gray-700 list-decimal pl-5 space-y-1">
                                    <li>اختر اللون الأساسي باستخدام منتقي الألوان</li>
                                    <li>اختر نوع لوحة الألوان (متكاملة، متشابهة، إلخ)</li>
                                    <li>انقر على زر "Generate Palette" لإنشاء لوحة الألوان</li>
                                    <li>ستظهر لوحة الألوان أسفل الزر في القسم المخصص</li>
                                </ol>
                            </div>

                            <div className="mb-3">
                                <label className="block text-sm font-medium mb-1">
                                    <span className="bg-yellow-100 px-2 py-0.5 rounded text-yellow-800 mr-1">1</span>
                                    Base Color
                                </label>
                                <div className="flex items-center">
                                    <input
                                        type="color"
                                        value={baseColor}
                                        onChange={(e) => setBaseColor(e.target.value)}
                                        className="w-12 h-10 mr-2 border rounded cursor-pointer"
                                        disabled={isProcessing}
                                    />
                                    <InputText
                                        value={baseColor}
                                        onChange={(e) => setBaseColor(e.target.value)}
                                        placeholder="#RRGGBB"
                                        className="w-full"
                                        disabled={isProcessing}
                                    />
                                </div>
                            </div>

                            <div className="mb-3">
                                <label className="block text-sm font-medium mb-1">
                                    <span className="bg-yellow-100 px-2 py-0.5 rounded text-yellow-800 mr-1">2</span>
                                    Palette Type
                                </label>
                                <Dropdown
                                    value={paletteType}
                                    options={paletteTypeOptions}
                                    onChange={(e) => setPaletteType(e.value)}
                                    className="w-full"
                                    disabled={isProcessing}
                                />
                            </div>

                            <div className="flex justify-between mb-4">
                                <Button
                                    label="Reset"
                                    icon="pi pi-refresh"
                                    className="p-button-outlined p-button-sm"
                                    onClick={resetPaletteGenerator}
                                    disabled={isProcessing}
                                />
                                <Button
                                    label={isProcessing ? "Generating..." : "Generate Palette"}
                                    icon="pi pi-palette"
                                    loading={isProcessing}
                                    onClick={() => {
                                        handleGeneratePalette();
                                    }}
                                    disabled={isProcessing}
                                    className="p-button-sm p-button-raised p-button-primary"
                                    style={{
                                        backgroundColor: '#be185d',
                                        borderColor: '#be185d',
                                        fontWeight: 'bold',
                                        boxShadow: '0 4px 6px -1px rgba(190, 24, 93, 0.3)'
                                    }}
                                />
                            </div>

                            <div className="mt-4 p-3 border rounded bg-gradient-to-r from-pink-50 to-purple-50 shadow-sm">
                                <h5 className="text-sm font-medium mb-2 flex items-center">
                                    <span className="bg-pink-500 text-white text-xs px-2 py-1 rounded mr-2">لوحة الألوان</span>
                                    Generated Palette:
                                </h5>
                                {generatedPalette ? (
                                    <>
                                        <div className="flex space-x-2 mb-3">
                                            {generatedPalette.map((color, index) => (
                                                <div key={index} className="flex-1 flex flex-col items-center">
                                                    <div
                                                        className="w-full h-16 rounded border border-gray-200 mb-1 shadow-sm"
                                                        style={{ backgroundColor: color }}
                                                    ></div>
                                                    <span className="text-xs font-mono bg-white px-1 rounded border">{color}</span>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="mt-2 flex justify-end">
                                            <Button
                                                label="Copy All"
                                                icon="pi pi-copy"
                                                className="p-button-sm p-button-outlined"
                                                onClick={() => {
                                                    navigator.clipboard.writeText(generatedPalette.join(', '));
                                                    setResultMessage({
                                                        severity: 'info',
                                                        summary: 'Copied',
                                                        detail: 'Colors copied to clipboard'
                                                    });
                                                }}
                                            />
                                        </div>
                                    </>
                                ) : (
                                    <p className="text-sm text-gray-500 italic p-2 bg-white rounded border border-gray-100">
                                        لوحة الألوان ستظهر هنا بعد النقر على زر "Generate Palette"
                                    </p>
                                )}
                            </div>
                        </div>
                    </TabPanel>
                </TabView>
            </div>
        </div>
    );
};

export default AIToolsHub;
