import { useState, useEffect } from 'react';
import { useGetImagesMutation } from '@quires';
import { useDesignSpace } from "@contexts/DesignSpaceContext";

const LoadOnScroll = ({ fileType, refetch, setRefetch }) => {
    const { addElement } = useDesignSpace()
    const getUserImages = useGetImagesMutation()

    const [items, setItems] = useState([]);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);

    const fetchItems = async (page) => {
        setLoading(true);

        // Always use "image" as the file_type for the API call
        // We'll filter the results client-side based on the fileType prop
        // Log the fileType for debugging
        console.log(`Fetching images for fileType: ${fileType}`);

        await getUserImages.mutateAsync({
            user_id: localStorage.getItem("user_id"),
            file_type: "image",
        }, {
            onSuccess: (data) => {
                // Log the raw data for debugging
                console.log(`Received ${data.length} images from API`);

                // Apply client-side filtering based on fileType
                let filteredData = [...data];

                // Log a sample item to see its structure
                if (data.length > 0) {
                    console.log('Sample image item:', data[0]);
                }

                // Filter logic based on fileType
                if (fileType === 'profile') {
                    // For profile images, we'll use a more flexible approach
                    // Check various properties that might indicate a profile image
                    filteredData = data.filter(item => {
                        // Check file path
                        const path = item.file_path ? item.file_path.toLowerCase() : '';

                        // Check file name if available
                        const name = item.file_name ? item.file_name.toLowerCase() : '';

                        // Check metadata if available
                        const metadata = item.metadata ? JSON.stringify(item.metadata).toLowerCase() : '';

                        // Check tags if available
                        const tags = item.tags ? item.tags.join(' ').toLowerCase() : '';

                        // Check if any of these contain "profile" or "avatar"
                        return path.includes('profile') || path.includes('avatar') ||
                               name.includes('profile') || name.includes('avatar') ||
                               metadata.includes('profile') || metadata.includes('avatar') ||
                               tags.includes('profile') || tags.includes('avatar');
                    });
                    console.log(`Filtered to ${filteredData.length} profile images`);
                } else if (fileType === 'uploaded') {
                    // For uploaded images, we'll consider anything that's not a profile image
                    // This is a simple approach - you might want to refine this based on your data
                    filteredData = data.filter(item => {
                        // Check file path
                        const path = item.file_path ? item.file_path.toLowerCase() : '';

                        // Check file name if available
                        const name = item.file_name ? item.file_name.toLowerCase() : '';

                        // Check metadata if available
                        const metadata = item.metadata ? JSON.stringify(item.metadata).toLowerCase() : '';

                        // Check tags if available
                        const tags = item.tags ? item.tags.join(' ').toLowerCase() : '';

                        // Check if none of these contain "profile" or "avatar"
                        return !path.includes('profile') && !path.includes('avatar') &&
                               !name.includes('profile') && !name.includes('avatar') &&
                               !metadata.includes('profile') && !metadata.includes('avatar') &&
                               !tags.includes('profile') && !tags.includes('avatar');
                    });
                    console.log(`Filtered to ${filteredData.length} uploaded images`);
                } else {
                    console.log(`Using all ${filteredData.length} images (no filtering)`);
                }

                setItems(filteredData);
                setLoading(false);
                setRefetch(false);
            }
        })
    };

    useEffect(() => {
        if (refetch)
            fetchItems();
    }, [page, refetch]);

    const handleScroll = (e) => {
        const { scrollTop, scrollHeight, clientHeight } = e.target;
        if (scrollTop + clientHeight >= scrollHeight - 10 && hasMore && !loading) {
            setPage((prevPage) => prevPage + 1);
        }
    };

    return (
        <div className='flex flex-wrap items-start justify-start max-h-[300px] overflow-auto '
            style={{ maxHeight: fileType === "qr" ? "400px" : "300px" }}
            onScroll={handleScroll}
        >
            {items.length > 0 ? (
                items.map((item, index) => (
                    <div className='w-4/12 p-2 ' key={index}>
                        <img
                            onClick={() => { addElement(fileType === "qr" ? "qr" : "img", item.file_path) }}
                            loading="lazy"
                            src={item.file_path}
                            alt="holder"
                            className={fileType === "qr" ? "" : 'aspect-video'}
                        />
                    </div>
                ))
            ) : !loading ? (
                <div className="w-full py-10 text-center text-gray-500">
                    <div className="text-3xl mb-2">🖼️</div>
                    <div className="font-medium">No images found</div>
                    <div className="text-xs mt-1">
                        {fileType === 'profile' ? 'No profile images available' :
                         fileType === 'uploaded' ? 'No uploaded images available' :
                         'No images available'}
                    </div>
                </div>
            ) : null}

            {loading && <div className='w-full p-2'>Loading...</div>}
        </div>
    );
};

export default LoadOnScroll;
