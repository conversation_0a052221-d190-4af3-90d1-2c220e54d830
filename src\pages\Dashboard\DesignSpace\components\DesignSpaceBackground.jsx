import React, { useEffect, useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import white_logo from "@images/white_logo.svg";
import {
    IoShapesOutline,
    IoColorPaletteOutline,
    IoImageOutline,
    IoTextOutline,
    IoLayersOutline,
    IoGridOutline
} from 'react-icons/io5';
import {
    FaPencilRuler,
    FaPalette,
    FaFont,
    FaImage
} from 'react-icons/fa';
import {
    RxText,
    RxImage,
    RxPencil2
} from 'react-icons/rx';

const DesignSpaceBackground = () => {
    const { cardType } = useDesignSpace();
    const [particles, setParticles] = useState([]);
    const [beams, setBeams] = useState([]);
    const [designElements, setDesignElements] = useState([]);

    // Generate static particles for the background
    useEffect(() => {
        const generateParticles = () => {
            const newParticles = [];
            const colors = ['blue', 'purple', 'pink', 'indigo', 'cyan'];

            for (let i = 0; i < 10; i++) {
                newParticles.push({
                    id: i,
                    x: Math.random() * 100,
                    y: Math.random() * 100,
                    size: 20 + Math.random() * 40,
                    color: colors[Math.floor(Math.random() * colors.length)]
                });
            }

            setParticles(newParticles);
        };

        const generateBeams = () => {
            const newBeams = [];

            for (let i = 0; i < 5; i++) {
                newBeams.push({
                    id: i,
                    x: 10 + Math.random() * 80,
                    rotation: -45 + Math.random() * 90,
                    opacity: 0.05 + Math.random() * 0.1
                });
            }

            setBeams(newBeams);
        };

        // Generate static design elements
        const generateDesignElements = () => {
            const newElements = [];
            const icons = [
                <IoShapesOutline size={20} />,
                <IoColorPaletteOutline size={20} />,
                <IoImageOutline size={20} />,
                <IoTextOutline size={20} />,
                <IoLayersOutline size={20} />,
                <IoGridOutline size={20} />,
                <RxText size={20} />,
                <RxImage size={20} />,
                <RxPencil2 size={20} />,
                <FaPencilRuler size={20} />,
                <FaPalette size={20} />,
                <FaFont size={20} />,
                <FaImage size={20} />
            ];

            // Generate 30 static design elements
            for (let i = 0; i < 30; i++) {
                const x = Math.random() * 100; // % position
                const y = Math.random() * 100; // % position
                const size = Math.random() * 0.5 + 0.5; // scale factor 0.5-1.0
                const opacity = Math.random() * 0.15 + 0.05; // 0.05-0.2
                const rotation = Math.random() * 360; // 0-360 degrees
                const iconIndex = Math.floor(Math.random() * icons.length);

                newElements.push({
                    id: `design-element-${i}`,
                    x,
                    y,
                    size,
                    opacity,
                    rotation,
                    icon: icons[iconIndex]
                });
            }

            setDesignElements(newElements);
        };

        generateParticles();
        generateBeams();
        generateDesignElements();
    }, []);

    return (
        <div className="absolute inset-0 overflow-hidden stone-wall-background">
            {/* Static Particles */}
            <div className="absolute inset-0 opacity-20">
                {particles.map(particle => (
                    <div
                        key={particle.id}
                        className="absolute rounded-full filter blur-3xl"
                        style={{
                            top: `${particle.y}%`,
                            left: `${particle.x}%`,
                            width: `${particle.size}px`,
                            height: `${particle.size}px`,
                            backgroundColor: particle.color === 'blue' ? '#60a5fa' :
                                            particle.color === 'purple' ? '#a78bfa' :
                                            particle.color === 'pink' ? '#f472b6' :
                                            particle.color === 'indigo' ? '#818cf8' :
                                            '#67e8f9' // cyan
                        }}
                    ></div>
                ))}
            </div>

            {/* Static Light Beams */}
            <div className="absolute inset-0 opacity-15">
                {beams.map(beam => (
                    <div
                        key={beam.id}
                        className="absolute top-0 w-1.5 h-full bg-white"
                        style={{
                            left: `${beam.x}%`,
                            transform: `rotate(${beam.rotation}deg)`,
                            filter: 'blur(8px) brightness(1.2)',
                            opacity: beam.opacity * 1.5
                        }}
                    ></div>
                ))}
            </div>

            {/* Static Gradient Overlay */}
            <div
                className="absolute inset-0 opacity-30"
                style={{
                    background: 'linear-gradient(45deg, rgba(76, 29, 149, 0.3), rgba(124, 58, 237, 0.3), rgba(167, 139, 250, 0.3))',
                    backgroundSize: '400% 400%'
                }}
            ></div>

            {/* Static Design Elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none design-elements-container">
                {designElements.map((element) => (
                    <div
                        key={element.id}
                        className="absolute design-element"
                        style={{
                            left: `${element.x}%`,
                            top: `${element.y}%`,
                            opacity: element.opacity * 1.5,
                            color: 'rgba(255, 255, 255, 0.8)',
                            transform: `rotate(${element.rotation}deg) scale(${element.size})`,
                            zIndex: 1,
                            filter: `drop-shadow(0 0 4px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 8px rgba(255, 255, 255, 0.4))`
                        }}
                    >
                        {element.icon}
                    </div>
                ))}
            </div>

            {/* Static Logo Watermark */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="relative w-full max-w-4xl">
                    {/* Base logo layer */}
                    <img
                        src={white_logo}
                        alt="Logo Watermark"
                        className="w-full opacity-30"
                    />

                    {/* Static glow effect layers */}
                    <img
                        src={white_logo}
                        alt=""
                        className="absolute top-0 left-0 w-full"
                        style={{
                            filter: 'blur(5px) brightness(1.5) drop-shadow(0 0 10px #60a5fa)',
                            opacity: 0.7,
                            mixBlendMode: 'screen'
                        }}
                    />

                    <img
                        src={white_logo}
                        alt=""
                        className="absolute top-0 left-0 w-full"
                        style={{
                            filter: 'blur(10px) brightness(1.2) drop-shadow(0 0 15px #a78bfa)',
                            opacity: 0.5,
                            mixBlendMode: 'screen'
                        }}
                    />

                    {/* Static bright core */}
                    <img
                        src={white_logo}
                        alt=""
                        className="absolute top-0 left-0 w-full"
                        style={{
                            filter: 'brightness(2) drop-shadow(0 0 5px rgba(255,255,255,0.8))',
                            opacity: 0.9
                        }}
                    />
                </div>
            </div>

            {/* Static stone wall background */}
            <style jsx>{`
                .stone-wall-background {
                    background-color: #2d3748;
                    background-image:
                        linear-gradient(335deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(155deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(335deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(155deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(335deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(155deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(50, 50, 50, 0.4) 1px, transparent 1px),
                        linear-gradient(180deg, rgba(50, 50, 50, 0.4) 1px, transparent 1px),
                        radial-gradient(circle at 50% 50%, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0) 60%);
                    background-size:
                        20px 20px,
                        20px 20px,
                        100px 100px,
                        100px 100px,
                        200px 200px,
                        200px 200px,
                        40px 40px,
                        40px 40px,
                        100% 100%;
                    background-position:
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        center center;
                    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5);
                }
            `}</style>
        </div>
    );
};

export default DesignSpaceBackground;
