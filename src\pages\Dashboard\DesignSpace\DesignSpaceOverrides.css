/* Override styles to remove transparency from design space */

.design-space {
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: transform 0.3s ease, box-shadow 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  transform-style: preserve-3d;
  perspective: 1200px;
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  z-index: 10 !important;
  transform-origin: center center !important;
  will-change: transform;
}

/* Hide design elements from the design area */
.design-space-container {
  position: relative;
  z-index: 5;
}

.design-elements-container {
  z-index: 1;
  pointer-events: none;
}

/* Ensure design elements don't appear inside the design space */
.design-space .design-element {
  display: none !important;
}

/* Smart positioning for element controls */
.draggable-element.top-edge .element-controls {
  top: auto !important;
  bottom: -55px !important;
}

.draggable-element.top-edge .element-controls::after {
  bottom: auto !important;
  top: -6px !important;
}

.draggable-element.right-edge .element-controls {
  right: auto !important;
  left: -10px !important;
}

.draggable-element.right-edge .element-controls::after {
  right: auto !important;
  left: 15px !important;
}

/* Remove transparent overlays */
.design-space::before {
  display: none !important;
}

/* Keep shadow but make it more solid */
.design-space::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 5%;
  right: 5%;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.3) !important;
  filter: blur(8px);
  border-radius: 50%;
  z-index: -1;
  transform-origin: center;
  animation: none !important;
}

/* Remove hover effects that change transparency */
.design-space:hover {
  transform: none !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
}

/* Make corner marks more visible */
.design-space .corner-mark {
  border-color: rgba(0, 0, 0, 0.4) !important;
}

/* Remove forced white background */
.design-space > div {
  opacity: 1 !important;
}

/* Zoom functionality styles */
.design-space[data-design-space="true"] {
  transform-origin: center center !important;
  will-change: transform !important;
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
}

/* Ensure zoom container allows overflow for zoomed content */
.design-space-container {
  overflow: auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 100% !important;
  padding: 50px !important;
}

/* Smooth zoom transitions */
.design-space.zooming {
  transition: transform 0.3s ease !important;
}
.element-controls:hover {
  transform: none !important;
  margin: 0 !important;
  top: initial !important;
  bottom: initial !important;
  left: initial !important;
  right: initial !important;
}