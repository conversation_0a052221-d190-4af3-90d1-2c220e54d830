import { QueryClient, useMutation } from 'react-query';
import axiosInstance from '../config/Axios';

import { useGlobalContext } from '@contexts/GlobalContext';
import { handleErrors } from '@utils/helper';

//--------------upload image-------------- //
const uploadImage = async (payload) => {
    const { data } = await axiosInstance.post("/upload", payload);

    return data;
}

export const useUploadMutation = () => {
    const { showToast } = useGlobalContext();
    const queryClient = new QueryClient();

    return useMutation(uploadImage, {
        onError: (error) => {
            handleErrors(showToast, error)
        }
    })
}

//--------------get image-------------- //
const getUserImages = async (payload) => {
    const { data } = await axiosInstance.get(`/files?user_id=${payload?.user_id}&file_type=${payload?.file_type}`);

    return data.data;
}

export const useGetImagesMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(
        {
            mutationKey: 'getUserImages',
            mutationFn: getUserImages,
        },
        {
            onSuccess: async () => {
            },
            onError: (error) => {
                handleErrors(showToast, error)
            }
        })
}