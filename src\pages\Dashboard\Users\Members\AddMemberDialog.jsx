import { useEffect, useMemo, useRef, useState } from 'react'
import { useForm, Controller } from "react-hook-form";
import _, { isEmpty } from 'lodash';

import { getFormErrorMessage } from '@utils/helper'
import { useGlobalContext } from '@contexts/GlobalContext';
import {
  useCreateUserMutation,
  useUpdateUserMutation,
  useUploadMutation,
  useGetCompanies
} from '@quires';

import { SelectButton } from 'primereact/selectbutton';
import { InputNumber } from 'primereact/inputnumber';
import { classNames } from 'primereact/utils';
import { FileUpload } from 'primereact/fileupload';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Password } from 'primereact/password';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';

import CustomFields from './CustomFields';

const typeOptions = [
  { label: "Employee", value: "employee" },
  { label: "Visitor", value: "visitor" },
];

const userTypeOptions = [
  { label: 'Admin', value: 'admin' },
  { label: 'Manager', value: 'manager' },
];

const numberOptions = _.times(11, (index) => { return { name: index, value: index } })

function AddMemberDialog({ actionType, data }) {
  const { openDialog, dialogHandler, disableBtn, setDisableBtn, userType } = useGlobalContext();
  const { control, formState: { errors }, handleSubmit, reset, setValue, watch } = useForm();

  const { data: companyOption, isLoading } = useGetCompanies();
  const uploadImage = useUploadMutation()
  const updateUser = useUpdateUserMutation();
  const addUser = useCreateUserMutation();

  const [companyUserType, setCompanyUserType] = useState("employee");
  const [image, setImage] = useState({});
  const fileUploadRef = useRef(null);

  const [currentUserType] = useState(() => localStorage.getItem('user_type'));
  const isManager = currentUserType === 'manager';
  const formUserType = watch('user_type');

  // Mobile detection
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const onCancelUpload = () => {
    setImage({})
    fileUploadRef.current.clear();
  };

  const fileUpload = async () => {
    const formData = new FormData()
    formData.append("file", image);
    formData.append("file_type", "image")
    formData.append("user_id", localStorage.getItem("user_id"));

    const res = await uploadImage.mutateAsync(formData)
    return res?.file_url
  }

  const createHandler = async (payload) => {
    setDisableBtn(true);

    // Save the current user type before making API calls
    const originalUserType = localStorage.getItem('user_type');
    const originalUserRole = localStorage.getItem('user_role');

    try {
        if (!isEmpty(image)) {
            let fileURL = await fileUpload();
            payload = { ...payload, image: fileURL };
        }

        if (actionType !== "update" && isManager) {
            payload = {
                ...payload,
                email: ``,
                password: '',
                user_type: '',
                company_id: ''
            };
        }

        if (actionType !== "update") {
            delete payload.user_id;  
        }

        if (actionType === "update") {
            const userId = payload?.id;
            delete payload?.id;
            delete payload?.print_status;
            
            // Ensure all fields are properly formatted
            const updateData = {
                name: payload.name,
                email: payload.email,
                phone: payload.phone,
                position: payload.position,
                department: payload.department,
                type: payload.type,
                image: payload.image
            };
            
            await updateUser.mutateAsync({ id: userId, data: updateData });
        } else {
            await addUser.mutateAsync(payload);
        }
        
        // Ensure user_type hasn't changed
        if (localStorage.getItem('user_type') !== originalUserType) {
            localStorage.setItem('user_type', originalUserType);
        }
        if (localStorage.getItem('user_role') !== "manager") {
            localStorage.setItem('user_role', "manager");
        }
        
        reset();
        setDisableBtn(false);
        dialogHandler("addMember");
        
    } catch (error) {
        console.error("Error saving data:", error);
        setDisableBtn(false);
        
        // Also restore user type on error
        if (localStorage.getItem('user_type') !== originalUserType) {
            localStorage.setItem('user_type', originalUserType);
        }
    }
};

  useEffect(() => {
    if (companyUserType === "employee") {
      setValue("department", data?.department || "");
    }
  }, [companyUserType]);

  useEffect(() => {
    if (actionType === "update") {
      setCompanyUserType(data?.type)
      reset(data)
    } else {
      reset()
    }
  }, [actionType]);

  return (
    <Dialog visible={openDialog.addMember}
      style={{
        width: isMobile ? '95vw' : '55%',
        maxHeight: '90vh'
      }}
      breakpoints={{
        '960px': '95vw',
        '641px': '95vw'
      }}
      header={`${actionType === "update" ? "Update User" : "Create User"}`}
      modal className="p-fluid"
      onHide={() => dialogHandler("addMember")}
      contentStyle={{
        maxHeight: isMobile ? 'calc(90vh - 60px)' : 'auto',
        overflow: 'auto'
      }}
    >
<form onSubmit={handleSubmit(createHandler)} className="w-full flex flex-col justify-center">
  <div className={`col-full flex flex-wrap justify-start py-4 border-[gray] ${isMobile ? 'flex-col' : ''}`}>
    {/* Name */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field ">
        <label className="form-label text-sm"> Name </label>
        <span className="p-float-label">
          <Controller name="name" control={control}
            rules={{ required: 'Name is required.' }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                inputRef={field.ref}
                className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
              />
            )} />
        </span>
        {getFormErrorMessage('name', errors)}
      </div>
    </div>



    {/* Image Input */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`} >
      <label htmlFor="image" className={`orm-label text-sm`} >Image</label>
      <Controller name="image" control={control}
        rules={{ required: false }}
        render={({ field, fieldState }) => (
          <FileUpload
            mode="basic"
            name="image"
            accept="image/*"
            maxFileSize={1000000}
            customUpload
            inputRef={field.ref}
            onSelect={(e) => { setImage(e.files[0]); setValue("image", e.files[0]) }}
            ref={fileUploadRef}
            onClick={onCancelUpload}
            chooseOptions={{
              className: 'upload_separator',
              label: 'click to upload image...',
              style: {
                background: 'transparent',
                color: 'gray',
                width: "100%",
                border: "2px dashed #D9DEE3",
                fontWeight: "normal",
                fontSize: "14px",
              }
            }}
          />
        )} />
    </div>

        {/* Phone */}
        <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field ">
        <label className="form-label text-sm"> Phone </label>
        <span className="p-float-label">
          <Controller name="phone" control={control}
            rules={{ required: 'Phone is required.' }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                inputRef={field.ref}
                className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                keyfilter="num"
              />
            )} />
        </span>
        {getFormErrorMessage('phone', errors)}
      </div>
    </div>

    {/* Type */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <label htmlFor="" className='form-label text-sm'>Type</label>
      <Controller name="type" control={control}
        rules={{ required: actionType === "update" ? false : "type is required!" }}
        render={({ field, fieldState }) => (
          <Dropdown
            id={field.name} {...field}
            value={field.value}
            options={typeOptions}
            onChange={(e) => { field.onChange(e.value); setCompanyUserType(e.value) }}
            optionLabel="label"
            optionValue="value"
            inputRef={field.ref}
            placeholder="select..."
            className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
          />
        )
        } />
      {getFormErrorMessage('type', errors)}
    </div>

    {/* company id */}
    {userType === "admin" &&
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <label htmlFor="type" className='form-label text-sm'>Company</label>
        <Controller name="company_id" control={control}
          rules={{ required: actionType === "update" ? false : "type is required!" }}
          render={({ field, fieldState }) => (
            <Dropdown
              disabled={isLoading}
              id={field.name} {...field}
              value={field.value}
              options={companyOption}
              onChange={(e) => { field.onChange(e.value); }}
              optionLabel="name"
              optionValue="id"
              inputRef={field.ref}
              placeholder="select..."
              className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
            />
          )
          } />
        {getFormErrorMessage('company_id', errors)}
      </div>
    }

    {/* Position */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field ">
        <label className="form-label text-sm"> Position </label>
        <span className="p-float-label">
          <Controller name="position" control={control}
            rules={{ required: 'Position is required.' }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                inputRef={field.ref}
                className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
              />
            )} />
        </span>
        {getFormErrorMessage('position', errors)}
      </div>
    </div>

    {/* Department */}
    {companyUserType === "employee" &&
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field ">
          <label className="form-label text-sm capitalize" >  Department </label>
          <span className="p-float-label">
            <Controller name="department" control={control}
              rules={{ required: actionType === "update" ? false : `Department is required.` }}
              render={({ field, fieldState }) => (
                <InputText
                  id={field.name}
                  {...field}
                  inputRef={field.ref}
                  className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                />
              )} />
          </span>
          {getFormErrorMessage("department", errors)}
        </div>
      </div>
    }

    {/* Email*/}
    {!isManager && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field">
          <label className="form-label text-sm">Email</label>
          <span className="p-float-label ">
            <Controller name="email" control={control}
              rules={{
                required: "Email is required!",
                pattern: {
                  value: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
                  message: "Invalid email!",
                }
              }}
              render={({ field, fieldState }) => (
                <InputText
                  id={field.email}
                  {...field}
                  inputRef={field.ref}
                  className={`${classNames({ 'p-invalid': fieldState.invalid })}`} />
              )} />
          </span>
          {getFormErrorMessage('email', errors)}
        </div>
      </div>
    )}

    {/* Password */}
    {!isManager && (
      <div className={`form-password-toggle ${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field ">
          <label className="form-label text-sm" htmlFor="password"> Password </label>
          <span className="p-float-label">
            <Controller name="password" control={control}
              rules={{ required: actionType === "create" ? "Password is required" : false }}
              render={({ field, fieldState }) => (
                <Password
                  id={field.password}
                  {...field}
                  inputRef={field.ref}
                  className={`${classNames({ 'p-invalid': fieldState.invalid })}`}
                  toggleMask
                />
              )} />
          </span>
        </div>
      </div>
    )}

    {/* User Type */}
    {userType === 'admin' && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <label htmlFor="" className='form-label text-sm'>Role</label>
        <Controller name="user_type" control={control}
          rules={{ required: actionType === "update" ? false : "User type is required!" }}
          render={({ field, fieldState }) => (
            <Dropdown
              id={field.name} {...field}
              value={field.value}
              options={userTypeOptions}
              onChange={(e) => field.onChange(e.value)}
              optionLabel="label"
              optionValue="value"
              inputRef={field.ref}
              placeholder="Select user Role..."
              className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
            />
          )}
        />
        {getFormErrorMessage('user_type', errors)}
      </div>
    )}

    <CustomFields control={control} errors={errors} />
  </div>

  <div className={`col-full text-center flex items-end ${isMobile ? 'flex-col space-y-3 px-4' : 'justify-start px-24'} py-4`}>
    <Button
      label="Cancel"
      aria-label="Close"
      type="reset"
      className={`gray-btn ${isMobile ? 'w-full' : 'w-3/12 me-2'} text-center`}
      disabled={disableBtn || addUser.isLoading}
      data-bs-dismiss="modal"
      onClick={() => dialogHandler("addMember")} />

    <Button
      label="Submit"
      aria-label="Add"
      type="submit"
      className={`main-btn ${isMobile ? 'w-full' : 'w-auto ms-2'} text-center`}
      disabled={disableBtn}
      loading={addUser.isLoading || disableBtn}
    />
  </div>
</form>
    </Dialog>
  )
}

export default AddMemberDialog