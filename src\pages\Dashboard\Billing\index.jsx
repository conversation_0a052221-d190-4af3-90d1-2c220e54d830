import React, { useEffect, useState } from "react";
import Container from "@components/Container";
import axiosInstance from "../../../config/Axios";
import { useGlobalContext } from "@contexts/GlobalContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { FaSearch, FaEllipsisV } from "react-icons/fa";

export default function BillingHistory() {
  const [purchaseHistory, setPurchaseHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    package_name: '',
    status: '',
  });

  // Mobile responsiveness states
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);

  const userId = localStorage.getItem("user_id");
  const { showToast } = useGlobalContext();

  const statusOptions = [
    { label: 'All Statuses', value: '' },
    { label: 'Active', value: 'active' },
    { label: 'Expired', value: 'expired' },
    { label: 'Suspended', value: 'suspended' },
  ];

  useEffect(() => {
    const fetchPurchaseHistory = async () => {
      try {
        setLoading(true);

        if (!userId) {
          showToast("error", "Error", "User ID not found");
          return;
        }

        const { data } = await axiosInstance.get(
          `/packages/${userId}/packages_history`,
          {
            params: {
              package_name: filters.package_name,
              status: filters.status,
            }
          }
        );

        setPurchaseHistory(data.history_packages || []);
      } catch (error) {
        console.error("Error fetching purchase history:", error);
        showToast("error", "Error", "Failed to fetch purchase history");
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseHistory();
  }, [userId, filters, showToast]);

  // Mobile detection useEffect
  useEffect(() => {
    const handleResize = () => {
      const mobileView = window.innerWidth < 768;
      setIsMobile(mobileView);
      if (!mobileView) {
        setMobileActionMenuOpen(null); // Close mobile menu when switching to desktop
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const statusBodyTemplate = (rowData) => {
    const statusClass = rowData.status === "active"
      ? "bg-[#22C55E]"
      : rowData.status === "suspended"
        ? "bg-[#F59E0B]"
        : "bg-[#DC2626]";

    return (
      <span
        className={`text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusClass} min-w-[100px] inline-block text-center`}
      >
        {rowData.status}
      </span>
    );
  };

  const priceBodyTemplate = (rowData) => {
    return (
      <span
        className="bg-transparent text-[#00CC32] border-2 border-[#00CC32] rounded-[6px] font-bold text-sm py-2 px-3 inline-block text-center min-w-[100px]"
      >
        ${rowData.total_price}
      </span>
    );
  };

  const onFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Mobile list view component
  const MobileListView = () => {
    if (loading) {
      return (
        <div className="space-y-2">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="bg-white border rounded-lg p-4 shadow-sm animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex items-center flex-1">
                  <div className="w-12 h-12 bg-gray-300 rounded-lg mr-3"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                  </div>
                </div>
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (purchaseHistory.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No purchase history found</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {purchaseHistory.map((item, index) => (
          <div key={item.id || index} className="bg-white border rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center flex-1">
                {/* Package Icon */}
                <div className="w-15 h-15 bg-green-100 rounded-lg mr-3 flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4a2 2 0 0 0 1-1.73z" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/>
                    <path d="M3.27 6.96L12 12.01l8.73-5.05M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>

                {/* Package Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 truncate">{item.package_name}</h3>
                  <p className="text-sm text-gray-500">
                    <span
                      className="bg-transparent text-[#00CC32] border border-[#00CC32] rounded px-2 py-1 text-xs font-bold"
                    >
                      ${item.total_price}
                    </span>
                  </p>
                  <div className="flex items-center mt-1 space-x-2">
                    <span className="text-xs text-gray-400">Purchased: {item.purchased_at}</span>
                  </div>
                  <div className="flex items-center mt-1 space-x-2">
                    <span className="text-xs text-gray-400">Expires: {item.expiry_date}</span>
                    <span className="text-xs text-gray-400">• Cards: {item.card_limit}</span>
                  </div>
                  <div className="mt-2">
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-bold ${
                        item.status === 'active' ? 'bg-green-100 text-green-800' :
                        item.status === 'suspended' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}
                    >
                      {item.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Container className="flex flex-col h-full">
      <div className="w-full mb-5">
        <h1 className="text-2xl font-bold">Billing & Purchase History</h1>
        {/* <p className="text-gray-600 mt-2">View your package purchase history and billing information</p> */}
      </div>

      {/* Search and Filter Section - Mobile responsive */}
      <div className={`w-full mb-4 ${isMobile ? 'space-y-3' : 'flex justify-between items-center'}`}>
        {/* Search Input */}
        <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search is disabled until further notice."   //was "Search by package name..."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={filters.package_name}
            onChange={(e) => onFilterChange('package_name', e.target.value)}
            disabled
          />
        </div>

        {/* Status Filter */}
        <div className={`flex items-center ${isMobile ? 'w-full' : 'ml-4'}`}>
          <span className="mr-2 text-gray-700">Status:</span>
          <Dropdown
            options={statusOptions}
            value={filters.status}
            onChange={(e) => onFilterChange('status', e.value)}
            placeholder="All Statuses"
            className={`shadow-md ${isMobile ? 'flex-1' : 'w-40'}`}
          />
        </div>
      </div>

      {/* Conditional rendering for mobile vs desktop */}
      <div className="w-full flex-grow overflow-hidden">
        {isMobile ? (
          // Mobile view
          <div className="h-full overflow-y-auto">
            <MobileListView />
          </div>
        ) : (
          // Desktop view
          <DataTable
            value={purchaseHistory}
            loading={loading}
            className="table w-full border"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25, 50]}
            emptyMessage="No purchase history found"
            header={null} // No header since we moved search/filter outside
            scrollable
            scrollHeight="100%" // Use a more conservative height calculation
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
          >
          <Column
            field="package_name"
            header="Package Name"
            sortable
          />
          <Column
            field="total_price"
            header="Total Price"
            body={priceBodyTemplate}
            sortable
          />
          <Column
            field="purchased_at"
            header="Purchase Date"
            sortable
          />
          <Column
            field="expiry_date"
            header="Expiry Date"
            sortable
          />
          <Column
            field="card_limit"
            header="Allowed Cards Count"
            sortable
          />
          <Column
            field="status"
            header="Status"
            body={statusBodyTemplate}
            sortable
          />
          </DataTable>
        )}
      </div>
    </Container>
  );
}
