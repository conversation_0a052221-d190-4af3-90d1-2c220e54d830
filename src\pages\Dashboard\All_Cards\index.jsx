import React, { useState, useRef } from 'react';
import { Tooltip } from 'primereact/tooltip';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { TfiTrash } from 'react-icons/tfi';
import { FaRegEye } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Container from '@components/Container';
import CreateCardForm from '../Backages/CreateCardForm';
import { useFetchCards } from "../../../quires/useGetCards ";
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';

const CardsDataTable = () => {
  const { data: cards, isLoading, isError, error, refetch } = useFetchCards();
  const [isCreateCardModalOpen, setIsCreateCardModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  const openCreateCardModal = () => {
    setIsEditMode(false);
    setIsCreateCardModalOpen(true);
  };

  const handleEdit = (rowData) => {
    setEditData(rowData);
    setIsEditMode(true);
    setIsCreateCardModalOpen(true);
  };

  const resetEditMode = () => {
    setIsEditMode(false);
    setEditData(null);
  };

  const handleDelete = (id) => {
    confirmDialog({
      message: 'Are you sure you want to delete this card?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteCard(id),
      reject: () => {}
    });
  };

  const deleteCard = async (id) => {
    try {
      const response = await fetch(`${backendUrl}/cards/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Card deleted successfully',
          life: 3000
        });
        refetch();
      } else {
        const errorData = await response.json();
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: errorData.message || 'Failed to delete card',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while deleting card',
        life: 3000
      });
    }
  };

  const actionsBodyTemplate = (rowData) => (
    <div className="flex justify-around">
      <Tooltip target=".edit-icon" content="Edit" position="top" />
      <button className="edit-icon" onClick={() => handleEdit(rowData)}>
        <FiEdit className="text-yellow-500" size={20} />
      </button>

      <Tooltip target=".delete-icon" content="Delete" position="top" />
      <button className="delete-icon" onClick={() => handleDelete(rowData.id)}>
        <TfiTrash className="text-red-500" size={20} />
      </button>
    </div>
  );

  if (isLoading) return <p>Loading...</p>;
  if (isError) return <p>Error: {error.message}</p>;

  return (
    <Container>
      <Toast ref={toast} />
      <ConfirmDialog />
      <div className="w-full flex justify-between items-center">
        <div className="w-4/12">
          <h1 className="text-xl font-bold">Card Management</h1>
        </div>

        <div className="w-8/12 flex justify-end">
          <button
            className="main-btn text-md shadow-md"
            onClick={openCreateCardModal}
          >
            Create New Card
          </button>
        </div>
      </div>

      <CreateCardForm
        isModalOpen={isCreateCardModalOpen}
        setIsModalOpen={setIsCreateCardModalOpen}
        fetchCards={refetch}
        editData={editData}
        isEditMode={isEditMode}
        resetEditMode={resetEditMode}
        lazy
        filterDisplay="row"
        responsiveLayout="stack"
        breakpoint="960px"
        dataKey="id"
        paginator
        className="table border"

        rowsPerPageOptions={[5, 25, 50, 100]}

        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"

        scrollable
        scrollHeight="calc(100vh - 400px)"
      />

      <DataTable value={cards?.data || []} paginator rows={10} className="mt-4"
                        scrollable
                  scrollHeight="100%"
      >
        <Column field="name" header="Card Name" />
        <Column field="number" header="Card Number" />
        <Column field="card_type.type_of_connection" header="Card Type of Connection" />
        <Column field="card_type.name" header="Card Type" />
        <Column field="manager_name" header="Manager Name" className="text-left" />
        
        <Column body={actionsBodyTemplate} header="Actions" />
      </DataTable>
    </Container>
  );
};

export default CardsDataTable;