import { useF<PERSON>, Controller } from 'react-hook-form';


import { getFormErrorMessage } from '@utils/helper'
import { useDesignSpace } from "@contexts/DesignSpaceContext";
import { useUploadMutation } from '@quires';

import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { useMemo, useState } from 'react';
import { isEmpty } from 'lodash';
import LoadOnScroll from './LoadOnScroll';

function QrCodeSettings() {
  const { addElement } = useDesignSpace();
  const { formState: { errors }, handleSubmit, control, watch, reset } = useForm();
  const [refetch, setRefetch] = useState(true);

  const uploadImage = useUploadMutation()

  const onSubmit = async (data) => {
    await uploadImage.mutateAsync(
      {
        user_id: localStorage.getItem("user_id"),
        file_type: "qr",
        ...data,
      },
      {
        onSuccess: (data) => {
          addElement("qr", data?.file_url)
          setRefetch(true)
          reset()
        }
      }
    )
  }

  const isDisabled = useMemo(() => isEmpty(watch("data")), [watch("data")])
  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
        <div className="mb-2 w-full">
          <div className="field">
            <span className="p-float-label mt-2">
              <Controller name="data" control={control}
                rules={{ required: "This field is required!" }}
                render={({ field, fieldState }) => (
                  <InputText
                    id={field.name}
                    {...field}
                    inputRef={field.ref}
                    placeholder="URL"
                    className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                )} />
            </span>
            {getFormErrorMessage('data', errors)}
          </div>
        </div>


        <button
          className={`w-6/12 me-1 my-2 ${isDisabled ? "gray-btn" : "main-btn"} ms-auto`}
          disabled={isDisabled}
          type='submit'
        >
          Add Qr
        </button>
      </form>

      <div className="flex flex-col justify-start m-2">
        <h4 className="font-bold mt-4 mb-2 text-[#676666]">Library</h4>
        <LoadOnScroll fileType="qr" refetch={refetch} setRefetch={setRefetch} />
      </div>
    </>
  )
}

export default QrCodeSettings