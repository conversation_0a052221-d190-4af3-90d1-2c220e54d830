import React, { useState, useEffect } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useGlobalContext } from '@contexts/GlobalContext';

// Icons
import { FiRotateCcw, FiRotateCw, FiLock, FiUnlock, FiZoomIn, FiZoomOut, FiImage, FiFilter, FiSave, FiX, FiMinus, FiPlus, FiMaximize } from 'react-icons/fi';
import {
    MdOutlineContentCopy,
    MdContentPaste,
    MdOutlineDeleteOutline,
    MdOutlineFormatColorFill,
    MdOutlineBorderColor,
    MdOutlineBrightness6,
    MdOutlineContrast,
    MdOutlineBlurOn,
    MdOutlineOpacity
} from 'react-icons/md';
import {
    BiSolidLayer,
    BiSolidLayerPlus,
    BiSolidLayerMinus,
    BiGroup,
    <PERSON>iLay<PERSON>,
    <PERSON>i<PERSON>djust,
    BiCrop
} from 'react-icons/bi';
import { IoIosArrowDown } from 'react-icons/io';
import { TbArrowsMaximize, TbPhotoEdit } from 'react-icons/tb';

// Dropdown menus
const FileMenu = ({ isOpen, onClose, onAction }) => {
    if (!isOpen) return null;

    const handleAction = (action) => {
        onAction(action);
        onClose();
    };

    return (
        <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md z-50 w-48">
            <ul className="py-1">
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('new')}
                >
                    <span className="mr-2">New</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+N</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('open')}
                >
                    <span className="mr-2">Open</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+O</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('save')}
                >
                    <span className="mr-2">Save</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+S</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('download')}
                >
                    Download
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('print')}
                >
                    Print
                </li>
            </ul>
        </div>
    );
};

const EditMenu = ({ isOpen, onClose, onAction }) => {
    if (!isOpen) return null;

    const handleAction = (action) => {
        onAction(action);
        onClose();
    };

    return (
        <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md z-50 w-48">
            <ul className="py-1">
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('undo')}
                >
                    <span className="mr-2">Undo</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+Z</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('redo')}
                >
                    <span className="mr-2">Redo</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+Y</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('cut')}
                >
                    <span className="mr-2">Cut</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+X</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('copy')}
                >
                    <span className="mr-2">Copy</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+C</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('paste')}
                >
                    <span className="mr-2">Paste</span>
                    <span className="text-xs text-gray-500 ml-auto">Ctrl+V</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('delete')}
                >
                    Delete
                </li>
            </ul>
        </div>
    );
};

const ViewMenu = ({ isOpen, onClose, onAction }) => {
    if (!isOpen) return null;

    const handleAction = (action) => {
        onAction(action);
        onClose();
    };

    return (
        <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md z-50 w-48">
            <ul className="py-1">
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('zoomIn')}
                >
                    Zoom In
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('zoomOut')}
                >
                    Zoom Out
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('fitScreen')}
                >
                    Fit Screen
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('showRulers')}
                >
                    Show Rulers
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleAction('showGrid')}
                >
                    Show Grid
                </li>
            </ul>
        </div>
    );
};

const ImageEditMenu = ({ isOpen, onClose, onAction }) => {
    if (!isOpen) return null;

    const handleAction = (action) => {
        onAction(action);
        onClose();
    };

    return (
        <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md z-50 w-48">
            <ul className="py-1">
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('brightness')}
                >
                    <MdOutlineBrightness6 className="mr-2" />
                    <span>Brightness</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('contrast')}
                >
                    <MdOutlineContrast className="mr-2" />
                    <span>Contrast</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('blur')}
                >
                    <MdOutlineBlurOn className="mr-2" />
                    <span>Blur</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('opacity')}
                >
                    <MdOutlineOpacity className="mr-2" />
                    <span>Opacity</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('crop')}
                >
                    <BiCrop className="mr-2" />
                    <span>Crop</span>
                </li>
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('filters')}
                >
                    <MdOutlinePhotoFilter className="mr-2" />
                    <span>Filters</span>
                </li>
                {/* <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleAction('advancedEdit')}
                >
                    <MdOutlineAutoFixHigh className="mr-2" />
                    <span>Advanced Edit</span>
                </li> */}
            </ul>
        </div>
    );
};

const CanvaToolbar = ({ updateTemplateData, onImageGenerationStart }) => {
    const {
        selectedIds,
        undo,
        redo,
        toggleLock,
        lockedElements,
        groupElements,
        ungroupElements,
        groups,
        zoom,
        zoomLevel,
        bringToFront,
        sendToBack,
        updateElement,
        elements,
        designSpaceRef,
        setSelectedIds,
        canvasBackground,
        canvasBackgroundStyle,
        setElements
    } = useDesignSpace();

    const { dialogHandler } = useGlobalContext();

    const [activeMenu, setActiveMenu] = useState(null);
    const [clipboardContent, setClipboardContent] = useState(null);
    const [showImageEditor, setShowImageEditor] = useState(false);
    const [selectedImageId, setSelectedImageId] = useState(null);
    const [formData, setFormData] = useState({});

    const handleMenuClick = (menu) => {
        if (activeMenu === menu) {
            setActiveMenu(null);
        } else {
            setActiveMenu(menu);
        }
    };

    const closeAllMenus = () => {
        setActiveMenu(null);
    };

    const handleFileAction = (action) => {
        switch(action) {
            case 'new':
                // Handle new file
                console.log('New file action');
                break;
            case 'open':
                // Handle open file
                console.log('Open file action');
                break;
            case 'save':
                // Handle save file
                console.log('Save file action');
                break;
            case 'download':
                // Handle download file
                console.log('Download file action');
                break;
            case 'print':
                // Handle print file
                console.log('Print file action');
                break;
            default:
                break;
        }
    };

    const handleEditAction = (action) => {
        switch(action) {
            case 'undo':
                undo();
                break;
            case 'redo':
                redo();
                break;
            case 'cut':
                // Handle cut
                if (selectedIds.length > 0) {
                    const selectedElements = elements.filter(el => selectedIds.includes(el.id));
                    setClipboardContent(selectedElements);
                    // Remove elements
                    const updatedElements = elements.filter(el => !selectedIds.includes(el.id));
                    setElements(updatedElements);
                    setSelectedIds([]);
                }
                break;
            case 'copy':
                // Handle copy
                if (selectedIds.length > 0) {
                    const selectedElements = elements.filter(el => selectedIds.includes(el.id));
                    setClipboardContent(selectedElements);
                }
                break;
            case 'paste':
                // Handle paste
                if (clipboardContent) {
                    const newElements = clipboardContent.map(el => ({
                        ...el,
                        id: `element-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                        x: el.x + 20,
                        y: el.y + 20
                    }));
                    setElements([...elements, ...newElements]);
                    setSelectedIds(newElements.map(el => el.id));
                }
                break;
            case 'delete':
                // Handle delete
                if (selectedIds.length > 0) {
                    // Filter out the selected elements while preserving the background
                    const updatedElements = elements.filter(el => !selectedIds.includes(el.id));
                    setElements(updatedElements);
                    setSelectedIds([]);
                }
                break;
            default:
                break;
        }
    };

    const handleViewAction = (action) => {
        switch(action) {
            case 'zoomIn':
                console.log('View menu: Zoom In');
                handleZoomIn();
                break;
            case 'zoomOut':
                console.log('View menu: Zoom Out');
                handleZoomOut();
                break;
            case 'fitScreen':
                console.log('View menu: Fit Screen');
                handleZoomReset();
                break;
            case 'showRulers':
                // Handle show rulers
                console.log('Show rulers action');
                break;
            case 'showGrid':
                // Handle show grid
                console.log('Show grid action');
                break;
            default:
                break;
        }
    };

    const handleImageAction = (action) => {
        if (selectedIds.length === 1) {
            const element = elements.find(el => el.id === selectedIds[0]);
            if (element && element.type === 'img') {
                setSelectedImageId(element.id);

                switch(action) {
                    case 'brightness':
                    case 'contrast':
                    case 'blur':
                    case 'opacity':
                    case 'crop':
                    case 'filters':
                        // These could be handled in a dedicated panel
                        console.log(`Image ${action} action`);
                        break;
                    case 'advancedEdit':
                        setShowImageEditor(true);
                        break;
                    default:
                        break;
                }
            }
        }
    };

    const isElementLocked = (id) => {
        return lockedElements.includes(id);
    };

    const isElementInGroup = (id) => {
        return Object.values(groups).some(group => group.includes(id));
    };

    const getGroupForElement = (id) => {
        return Object.keys(groups).find(groupId => groups[groupId].includes(id));
    };

    const handleZoomIn = () => {
        const newZoomLevel = Math.min(zoomLevel + 10, 200);
        console.log('Zoom In button clicked. Current:', zoomLevel, 'New:', newZoomLevel);
        zoom(newZoomLevel);
    };

    const handleZoomOut = () => {
        const newZoomLevel = Math.max(zoomLevel - 10, 50);
        console.log('Zoom Out button clicked. Current:', zoomLevel, 'New:', newZoomLevel);
        zoom(newZoomLevel);
    };

    const handleZoomReset = () => {
        console.log('Zoom Reset button clicked. Current:', zoomLevel, 'Resetting to: 100');
        zoom(100);
    };

    // Add keyboard shortcuts for zoom
    useEffect(() => {
        const handleKeyPress = (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '+':
                        e.preventDefault();
                        handleZoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        handleZoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        handleZoomReset();
                        break;
                    default:
                        break;
                }
            }
        };

        window.addEventListener('keydown', handleKeyPress);
        return () => window.removeEventListener('keydown', handleKeyPress);
    }, [zoomLevel]);

    const handleLockToggle = () => {
        if (selectedIds.length > 0) {
            toggleLock(selectedIds);
        }
    };

    const handleGroup = () => {
        if (selectedIds.length > 1) {
            groupElements(selectedIds);
        }
    };

    const handleUngroup = () => {
        if (selectedIds.length === 1) {
            const groupId = selectedIds[0];
            if (groupId.startsWith('group_')) {
                ungroupElements(groupId);
            }
        }
    };

    const handleImageEdit = (type, value) => {
        if (selectedIds.length === 1) {
            const element = elements.find(el => el.id === selectedIds[0]);
            if (element && element.type === 'img') {
                updateElement(element.id, { [type]: value });
            }
        }
    };

    // Save Design Handler
    const saveDesignHandler = async () => {
        try {
            setSelectedIds([]);

            // تحويل النصوص إلى متغيرات في DOM
            const textElements = designSpaceRef.current.querySelectorAll('.user-data');
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value) {
                    element.textContent = `{{${value}}}`;
                }
            });

            const content = designSpaceRef.current.innerHTML;

            // Get the current background from the DOM directly
            const designSpaceContent = document.getElementById('design-space-content');
            let actualBackground = '';
            let actualBackgroundStyle = null;

            if (designSpaceContent) {
                // Get the computed style
                const computedStyle = window.getComputedStyle(designSpaceContent);
                
                // Get background color and image
                const bgColor = computedStyle.backgroundColor;
                const bgImage = computedStyle.backgroundImage;

                console.log("Raw background values:", {
                    backgroundColor: bgColor,
                    backgroundImage: bgImage
                });

                // Handle solid color
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    actualBackground = bgColor;
                }
                // Handle gradient or image
                else if (bgImage && bgImage !== 'none') {
                    actualBackground = bgImage;
                    actualBackgroundStyle = {
                        backgroundSize: computedStyle.backgroundSize,
                        backgroundPosition: computedStyle.backgroundPosition,
                        backgroundRepeat: computedStyle.backgroundRepeat
                    };
                }

                console.log("Processed background values:", {
                    background: actualBackground,
                    backgroundStyle: actualBackgroundStyle
                });
            }

            // تحويل النصوص في init_template إلى متغيرات
            const initTemplateElements = elements.map(el => {
                if (el.type === 'text' && el.value) {
                    return {
                        ...el,
                        value: `{{${el.value}}}`
                    };
                }
                return el;
            });

            // Create template data with background information
            const templateData = {
                htmlTemplate: content,
                initTemplate: JSON.stringify(initTemplateElements),
                background: actualBackground || canvasBackground,
                backgroundStyle: actualBackgroundStyle ? JSON.stringify(actualBackgroundStyle) : ''
            };

            console.log("Final template data:", templateData);

            // Update template data
            if (typeof updateTemplateData === 'function') {
                updateTemplateData(templateData);
            }

            // Open save dialog
            dialogHandler("createDesignTemplate");

            // إعادة النصوص إلى حالتها الأصلية
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value && value.startsWith('{{') && value.endsWith('}}')) {
                    element.textContent = value.slice(2, -2);
                }
            });
        } catch (error) {
            console.error("Error saving design:", error);
        }
    };

    return (
        <div className="canva-toolbar w-full bg-white border-b border-gray-200 flex items-center justify-between px-0 py-2">
            <div className="flex items-center space-x-4">
                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Undo/Redo */}
                <div className="flex items-center space-x-2">
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={undo}
                        title="Undo"
                    >
                        <FiRotateCcw />
                    </button>
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={redo}
                        title="Redo"
                    >
                        <FiRotateCw />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Zoom Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomOut();
                        }}
                        className="p-2 hover:bg-gray-100 rounded transition-colors"
                        title="Zoom Out (Ctrl + -)"
                        disabled={zoomLevel <= 50}
                    >
                        <FiMinus />
                    </button>
                    <span className="text-sm font-medium min-w-[50px] text-center">{zoomLevel}%</span>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomIn();
                        }}
                        className="p-2 hover:bg-gray-100 rounded transition-colors"
                        title="Zoom In (Ctrl + +)"
                        disabled={zoomLevel >= 200}
                    >
                        <FiPlus />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomReset();
                        }}
                        className="p-2 hover:bg-gray-100 rounded transition-colors"
                        title="Reset Zoom (Ctrl + 0)"
                    >
                        <FiMaximize />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Element Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleLockToggle}
                        disabled={selectedIds.length === 0}
                        title={selectedIds.length > 0 && isElementLocked(selectedIds[0]) ? "Unlock" : "Lock"}
                    >
                        {selectedIds.length > 0 && isElementLocked(selectedIds[0]) ? <FiUnlock /> : <FiLock />}
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleGroup}
                        disabled={selectedIds.length < 2}
                        title="Group"
                    >
                        <BiGroup />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 || !selectedIds[0].startsWith('group_') ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleUngroup}
                        disabled={selectedIds.length !== 1 || !selectedIds[0].startsWith('group_')}
                        title="Ungroup"
                    >
                        <BiLayer />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Layer Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && bringToFront(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Bring to Front"
                    >
                        <BiSolidLayerPlus />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && sendToBack(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Send to Back"
                    >
                        <BiSolidLayerMinus />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Image Editing Tools */}
                <div className="relative">
                    <button
                        className={`px-3 py-1 rounded hover:bg-gray-100 flex items-center ${activeMenu === 'image' ? 'bg-gray-100' : ''} ${!selectedIds.length || elements.find(el => el.id === selectedIds[0])?.type !== 'img' ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length && elements.find(el => el.id === selectedIds[0])?.type === 'img' ? handleMenuClick('image') : null}
                        disabled={!selectedIds.length || elements.find(el => el.id === selectedIds[0])?.type !== 'img'}
                    >
                        <TbPhotoEdit className="mr-1" />
                        Image <IoIosArrowDown className="ml-1" />
                    </button>
                    <ImageEditMenu isOpen={activeMenu === 'image'} onClose={closeAllMenus} onAction={handleImageAction} />
                </div>

                {/* Image Editor Dialog */}
                {showImageEditor && selectedImageId && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
                            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                                <h2 className="text-xl font-semibold">Advanced Image Editor</h2>
                                <button
                                    className="p-2 rounded-full hover:bg-gray-100"
                                    onClick={() => setShowImageEditor(false)}
                                >
                                    <FiX />
                                </button>
                            </div>
                            <div className="p-4">
                                <ImageEditor
                                    imageId={selectedImageId}
                                    onClose={() => setShowImageEditor(false)}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Save Design Button */}
            <div className="flex items-center">
                <button
                    className="main-btn text-md shadow-sm flex items-center"
                    onClick={saveDesignHandler}
                    style={{
                        backgroundColor: 'var(--main_color, #00c3ac)',
                        borderColor: 'var(--main_color, #00c3ac)',
                        fontWeight: 'bold',
                        borderRadius: '6px',
                        padding: '6px 10px',
                        color: '#ffffff'
                    }}
                >
                    <FiSave className="mr-2" style={{ color: '#ffffff' }} />
                    <span style={{ color: '#ffffff' }}>Save Design</span>
                </button>
            </div>
        </div>
    );
};

export default CanvaToolbar;
