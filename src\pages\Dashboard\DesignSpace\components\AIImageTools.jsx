import React, { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { ProgressBar } from 'primereact/progressbar';
import { Dropdown } from 'primereact/dropdown';
import { Slider } from 'primereact/slider';
import { Divider } from 'primereact/divider';
import { Message } from 'primereact/message';

// Icons
import { MdOutlineAutoFixHigh, MdOutlinePhotoFilter, MdOutlineColorLens } from 'react-icons/md';
import { FaMagic, FaRobot } from 'react-icons/fa';
import { BiPaint } from 'react-icons/bi';

// Mock AI image processing functions
const mockAIProcessing = (type, options = {}) => {
    return new Promise((resolve) => {
        // Simulate processing time
        setTimeout(() => {
            let result;

            switch(type) {
                case 'enhance':
                    result = {
                        success: true,
                        filters: {
                            brightness: 110,
                            contrast: 120,
                            saturation: 110,
                            blur: 0
                        },
                        message: 'Image enhanced successfully'
                    };
                    break;

                case 'removeBackground':
                    result = {
                        success: true,
                        // In a real implementation, this would be a URL to the processed image
                        value: 'https://img.freepik.com/free-photo/woman-with-shopping-bags-studio-yellow-background-isolated_1303-14294.jpg',
                        message: 'Background removed successfully'
                    };
                    break;

                case 'styleTransfer':
                    result = {
                        success: true,
                        filters: {
                            brightness: options.style === 'cartoon' ? 120 : 100,
                            contrast: options.style === 'cartoon' ? 130 : 110,
                            saturation: options.style === 'oil-painting' ? 130 : 100,
                            sepia: options.style === 'vintage' ? 50 : 0,
                            hue: options.style === 'pop-art' ? 180 : 0,
                            grayscale: options.style === 'noir' ? 100 : 0
                        },
                        message: `Style transfer (${options.style}) applied successfully`
                    };
                    break;

                case 'generate':
                    // Different sample images for different prompts
                    let imageUrl = 'https://img.freepik.com/free-photo/painting-mountain-lake-with-mountain-background_188544-9126.jpg';

                    if (options.prompt && options.prompt.toLowerCase().includes('sunset')) {
                        imageUrl = 'https://img.freepik.com/free-photo/beautiful-tropical-beach-sea-ocean-with-coconut-palm-tree-sunrise-time_74190-7454.jpg';
                    } else if (options.prompt && options.prompt.toLowerCase().includes('city')) {
                        imageUrl = 'https://img.freepik.com/free-photo/beautiful-manhattan-bridge-new-york-usa_181624-48458.jpg';
                    } else if (options.prompt && options.prompt.toLowerCase().includes('portrait')) {
                        imageUrl = 'https://img.freepik.com/free-photo/young-beautiful-woman-pink-warm-sweater-natural-look-smiling-portrait-isolated-long-hair_285396-896.jpg';
                    } else if (options.prompt && options.prompt.toLowerCase().includes('food')) {
                        imageUrl = 'https://img.freepik.com/free-photo/top-view-table-full-delicious-food-composition_23-2149141352.jpg';
                    }

                    result = {
                        success: true,
                        value: imageUrl,
                        message: 'Image generated successfully'
                    };
                    break;

                default:
                    result = {
                        success: false,
                        message: 'Unknown AI operation'
                    };
            }

            resolve(result);
        }, 2000); // Simulate 2 second processing time
    });
};

const AIImageTools = () => {
    const { selectedIds, elements, updateElement, addElement } = useDesignSpace();

    const [selectedImage, setSelectedImage] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [processingType, setProcessingType] = useState('');
    const [processingProgress, setProcessingProgress] = useState(0);
    const [resultMessage, setResultMessage] = useState(null);

    // AI Style Transfer options
    const [selectedStyle, setSelectedStyle] = useState(null);
    const styleOptions = [
        { label: 'Cartoon', value: 'cartoon' },
        { label: 'Oil Painting', value: 'oil-painting' },
        { label: 'Vintage', value: 'vintage' },
        { label: 'Pop Art', value: 'pop-art' },
        { label: 'Noir', value: 'noir' }
    ];

    // AI Text-to-Image options
    const [promptText, setPromptText] = useState('');
    const [generationStrength, setGenerationStrength] = useState(70);

    // Find the selected image
    React.useEffect(() => {
        if (selectedIds.length === 1) {
            const element = elements.find(el => el.id === selectedIds[0]);
            if (element && element.type === 'img') {
                setSelectedImage(element);
            } else {
                setSelectedImage(null);
            }
        } else {
            setSelectedImage(null);
        }
    }, [selectedIds, elements]);

    // Simulate progress during processing
    React.useEffect(() => {
        let progressInterval;

        if (isProcessing) {
            setProcessingProgress(0);
            progressInterval = setInterval(() => {
                setProcessingProgress(prev => {
                    const newProgress = prev + 5;
                    if (newProgress >= 100) {
                        clearInterval(progressInterval);
                        return 100;
                    }
                    return newProgress;
                });
            }, 100);
        }

        return () => {
            if (progressInterval) clearInterval(progressInterval);
        };
    }, [isProcessing]);

    // Handle AI image enhancement
    const handleEnhanceImage = async () => {
        if (!selectedImage) return;

        setIsProcessing(true);
        setProcessingType('enhance');
        setResultMessage(null);

        try {
            const result = await mockAIProcessing('enhance');

            if (result.success) {
                updateElement(selectedImage.id, {
                    filters: result.filters,
                    style: {
                        filter: `brightness(${result.filters.brightness}%) contrast(${result.filters.contrast}%) saturate(${result.filters.saturation}%) blur(${result.filters.blur}px)`
                    }
                });

                setResultMessage({
                    severity: 'success',
                    summary: 'Success',
                    detail: result.message
                });
            } else {
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: result.message
                });
            }
        } catch (error) {
            setResultMessage({
                severity: 'error',
                summary: 'Error',
                detail: 'An error occurred during processing'
            });
        } finally {
            setIsProcessing(false);
        }
    };

    // Handle AI background removal
    const handleRemoveBackground = async () => {
        if (!selectedImage) return;

        setIsProcessing(true);
        setProcessingType('removeBackground');
        setResultMessage(null);

        try {
            const result = await mockAIProcessing('removeBackground');

            if (result.success) {
                updateElement(selectedImage.id, {
                    value: result.value
                });

                setResultMessage({
                    severity: 'success',
                    summary: 'Success',
                    detail: result.message
                });
            } else {
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: result.message
                });
            }
        } catch (error) {
            setResultMessage({
                severity: 'error',
                summary: 'Error',
                detail: 'An error occurred during processing'
            });
        } finally {
            setIsProcessing(false);
        }
    };

    // Handle AI style transfer
    const handleStyleTransfer = async () => {
        if (!selectedImage || !selectedStyle) return;

        setIsProcessing(true);
        setProcessingType('styleTransfer');
        setResultMessage(null);

        try {
            const result = await mockAIProcessing('styleTransfer', { style: selectedStyle });

            if (result.success) {
                const filters = result.filters;
                const filterString = `brightness(${filters.brightness}%) contrast(${filters.contrast}%) saturate(${filters.saturation}%) sepia(${filters.sepia}%) hue-rotate(${filters.hue}deg) grayscale(${filters.grayscale}%)`;

                updateElement(selectedImage.id, {
                    filters: filters,
                    style: {
                        filter: filterString
                    }
                });

                setResultMessage({
                    severity: 'success',
                    summary: 'Success',
                    detail: result.message
                });
            } else {
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: result.message
                });
            }
        } catch (error) {
            setResultMessage({
                severity: 'error',
                summary: 'Error',
                detail: 'An error occurred during processing'
            });
        } finally {
            setIsProcessing(false);
        }
    };

    // Handle AI text-to-image generation
    const handleGenerateImage = async () => {
        if (!promptText) return;

        setIsProcessing(true);
        setProcessingType('generate');
        setResultMessage(null);

        try {
            const result = await mockAIProcessing('generate', {
                prompt: promptText,
                strength: generationStrength
            });

            if (result.success) {
                // Add the generated image to the canvas
                addElement("img", result.value);

                setResultMessage({
                    severity: 'success',
                    summary: 'Success',
                    detail: result.message
                });

                // Clear the prompt
                setPromptText('');
            } else {
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: result.message
                });
            }
        } catch (error) {
            setResultMessage({
                severity: 'error',
                summary: 'Error',
                detail: 'An error occurred during processing'
            });
        } finally {
            setIsProcessing(false);
        }
    };

    if (!selectedImage && processingType !== 'generate') {
        return (
            <div className="ai-image-tools p-4 h-full flex flex-col">
                <div className="text-center flex-grow flex flex-col items-center justify-center">
                    <FaRobot size={48} className="text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium mb-2">AI Image Tools</h3>
                    <p className="text-gray-500 mb-4">Select an image to use AI tools or generate a new image</p>

                    <Button
                        label="Generate Image with AI"
                        icon="pi pi-image"
                        className="p-button-outlined"
                        onClick={() => setProcessingType('generate')}
                    />
                </div>
            </div>
        );
    }

    return (
        <div className="ai-image-tools h-full" style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', backgroundColor: 'white' }}>
            <div className="p-4 border-b border-gray-200 bg-white z-10" style={{ flexShrink: 0 }}>
                <h3 className="text-lg font-medium flex items-center">
                    <FaRobot className="mr-2 text-purple-600" />
                    AI Image Tools
                </h3>

                {isProcessing && (
                    <div className="mt-3">
                        <div className="flex justify-between mb-1">
                            <span className="text-sm">{processingType === 'enhance' ? 'Enhancing image...' :
                                processingType === 'removeBackground' ? 'Removing background...' :
                                processingType === 'styleTransfer' ? 'Applying style...' :
                                'Generating image...'}</span>
                            <span className="text-sm font-medium">{processingProgress}%</span>
                        </div>
                        <ProgressBar value={processingProgress} />
                    </div>
                )}

                {resultMessage && (
                    <div className="mt-3">
                        <Message
                            severity={resultMessage.severity}
                            text={resultMessage.detail}
                            style={{ width: '100%' }}
                        />
                    </div>
                )}
            </div>

            <div style={{
                flexGrow: 1,
                overflowY: 'auto',
                padding: '16px',
                height: '100%',
                position: 'relative'
            }}>
                <div style={{ position: 'absolute', width: '100%', left: 0, padding: '0 16px' }}>
                {processingType === 'generate' ? (
                    <div className="text-to-image-section">
                        <h4 className="text-md font-medium mb-3 flex items-center">
                            <BiPaint className="mr-2 text-green-600" />
                            Text to Image
                        </h4>

                        <div className="mb-4">
                            <label htmlFor="prompt" className="block text-sm font-medium mb-1">Describe the image you want</label>
                            <InputText
                                id="prompt"
                                value={promptText}
                                onChange={(e) => setPromptText(e.target.value)}
                                placeholder="e.g., A sunset over mountains with a lake"
                                className="w-full"
                                disabled={isProcessing}
                            />
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-1">Generation Strength: {generationStrength}%</label>
                            <Slider
                                value={generationStrength}
                                onChange={(e) => setGenerationStrength(e.value)}
                                min={10}
                                max={100}
                                disabled={isProcessing}
                            />
                        </div>

                        <div className="flex justify-between">
                            <Button
                                label="Back"
                                icon="pi pi-arrow-left"
                                className="p-button-outlined"
                                onClick={() => setProcessingType('')}
                                disabled={isProcessing}
                            />
                            <Button
                                label="Generate"
                                icon="pi pi-image"
                                onClick={handleGenerateImage}
                                disabled={!promptText || isProcessing}
                            />
                        </div>
                    </div>
                ) : (
                    <>
                        <div className="ai-tools-grid grid grid-cols-1 gap-4">
                            <div className="ai-tool-card p-4 border border-gray-200 rounded-lg hover:border-purple-300 transition-colors">
                                <div className="flex items-center mb-3">
                                    <MdOutlineAutoFixHigh className="text-blue-600 text-xl mr-2" />
                                    <h4 className="text-md font-medium">AI Enhancement</h4>
                                </div>
                                <p className="text-sm text-gray-600 mb-3">Automatically enhance your image with AI to improve quality, lighting, and colors.</p>
                                <Button
                                    label="Enhance Image"
                                    className="p-button-sm w-full"
                                    onClick={handleEnhanceImage}
                                    disabled={isProcessing}
                                />
                            </div>

                            <div className="ai-tool-card p-4 border border-gray-200 rounded-lg hover:border-purple-300 transition-colors">
                                <div className="flex items-center mb-3">
                                    <FaMagic className="text-purple-600 text-xl mr-2" />
                                    <h4 className="text-md font-medium">Remove Background</h4>
                                </div>
                                <p className="text-sm text-gray-600 mb-3">Automatically remove the background from your image with AI.</p>
                                <Button
                                    label="Remove Background"
                                    className="p-button-sm w-full"
                                    onClick={handleRemoveBackground}
                                    disabled={isProcessing}
                                />
                            </div>

                            <div className="ai-tool-card p-4 border border-gray-200 rounded-lg hover:border-purple-300 transition-colors">
                                <div className="flex items-center mb-3">
                                    <MdOutlinePhotoFilter className="text-green-600 text-xl mr-2" />
                                    <h4 className="text-md font-medium">AI Style Transfer</h4>
                                </div>
                                <p className="text-sm text-gray-600 mb-3">Apply artistic styles to your image using AI.</p>
                                <div className="mb-3">
                                    <Dropdown
                                        value={selectedStyle}
                                        options={styleOptions}
                                        onChange={(e) => setSelectedStyle(e.value)}
                                        placeholder="Select a style"
                                        className="w-full"
                                        disabled={isProcessing}
                                    />
                                </div>
                                <Button
                                    label="Apply Style"
                                    className="p-button-sm w-full"
                                    onClick={handleStyleTransfer}
                                    disabled={!selectedStyle || isProcessing}
                                />
                            </div>
                        </div>

                        <Divider align="center">
                            <span className="text-xs font-medium text-gray-500">OR</span>
                        </Divider>

                        <div className="text-center mt-4">
                            <Button
                                label="Generate New Image with AI"
                                icon="pi pi-plus"
                                className="p-button-outlined"
                                onClick={() => setProcessingType('generate')}
                                disabled={isProcessing}
                            />
                        </div>
                    </>
                )}
                </div>
            </div>
        </div>
    );
};

export default AIImageTools;
