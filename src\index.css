@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --main_color: #00c3ac;
  --main_color_hover: #02aa96;
  --gray: #dcdcdc;
}
body * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.main-color {
  background: var(--main_color);
}

.main-btn {
  color: white;
  border-radius: 6px;
  padding: 6px 10px;
  background-color: var(--main_color);
  border-color: var(--main_color);
  font-weight: bold;
  transition: all 0.5s;
}

.main-btn:hover {
  background-color: var(--main_color_hover);
}

.gray-btn {
  color: rgb(93, 93, 93);
  font-weight: bold;
  border-radius: 6px;
  padding: 6px 10px;
  background-color: var(--gray);
  border-color: var(--gray);
}

.auth-input {
  padding: 23px 30px;
  border-radius: 6px;
  border: 1px solid #8692a6;
  background-color: white !important;
}

.pass-input input {
  width: 100%;
}

.side-image-container {
  background-image: url("./assets/images/auth.jfif");
  background-position: center;
  background-size: cover;
}

.active_tab {
  font-weight: bold;
  background-color: #7de1d55e;
  border-radius: 10px;
}

.filter-field {
  border-right: 2px solid #d7d7d7;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active-bh {
  background-color: #7979795e;
}

/* Remove border from the TabMenu container */
.p-tabmenu.types-tab-menu {
  border: none;
}

/* Remove borders from individual tabs */
.p-tabmenu.types-tab-menu .p-tabmenu-nav {
  border: none;
}

/* Remove borders from tab items */
.p-tabmenu.types-tab-menu .p-tabmenuitem {
  border: none;
  box-shadow: none; /* Remove any shadows if present */
}

.p-tabmenu.types-tab-menu .p-tabmenuitem {
  border-color: var(--main_color_hover) !important;
}

/* Style the active tab */
.p-tabmenu.types-tab-menu .p-highlight * {
  color: var(--main_color);
}

.p-tabmenu.types-tab-menu:hover * {
  color: var(--main_color_hover);
}
/* Remove hover border effect */
.p-tabmenu.types-tab-menu .p-tabmenuitem:hover {
  border: none;
  background-color: #f0f0f0;
}

.p-selectbutton .p-button.p-highlight {
  background: var(--main_color_hover);
  border-color:var(--main_color_hover);
  color: #ffffff;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.3;
  }
}


.sticky-header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table-responsive {
  overflow-y: auto;
  height: calc(100vh - 300px);
}

body {
  overflow-x: hidden;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.sticky-header {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 50;
}

.sticky-header thead th {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 10;
  box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1);
}

/* Responsive DataTable Styles */
.p-datatable-responsive .p-datatable-tbody > tr > td .p-column-title {
  display: none;
}

@media screen and (max-width: 768px) {
  .p-datatable.p-datatable-responsive .p-datatable-thead > tr > th,
  .p-datatable.p-datatable-responsive .p-datatable-tfoot > tr > td {
    display: none !important;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td {
    text-align: left;
    display: block;
    width: 100%;
    float: left;
    clear: left;
    border: 0 none;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td .p-column-title {
    padding: 0.4rem;
    min-width: 30%;
    display: inline-block;
    margin: -0.4em 1em -0.4em -0.4rem;
    font-weight: bold;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td:last-child {
    border-bottom: 1px solid var(--surface-d);
  }
}

/* Responsive utilities */
.responsive-container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}
